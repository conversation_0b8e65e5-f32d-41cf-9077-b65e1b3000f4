"""
Professional Billing Management System
Handles user accounts, token transactions, and usage tracking
"""

import sqlite3
import uuid
import hashlib
import jwt
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pricing_engine import TokenPricingEngine, ServiceTier, TOKEN_PACKAGES

class BillingManager:
    """Professional billing and user management system"""
    
    def __init__(self, db_path: str = "billing.db", secret_key: str = "your-secret-key"):
        self.db_path = db_path
        self.secret_key = secret_key
        self.pricing_engine = TokenPricingEngine(db_path)
        self.init_database()
    
    def init_database(self):
        """Initialize additional billing tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # API keys table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_keys (
                key_id TEXT PRIMARY KEY,
                user_id TEXT,
                api_key TEXT UNIQUE,
                name TEXT,
                permissions TEXT,
                last_used TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIG<PERSON> KEY (user_id) REFERENCES users (user_id)
            )
        ''')
        
        # Subscriptions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS subscriptions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT,
                tier TEXT,
                status TEXT DEFAULT 'active',
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        ''')
        
        # Token purchases table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS token_purchases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT,
                package_name TEXT,
                tokens_purchased INTEGER,
                bonus_tokens INTEGER,
                amount_paid REAL,
                payment_method TEXT,
                transaction_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def create_user(self, email: str, password: str, tier: str = 'starter') -> Dict:
        """Create a new user account"""
        user_id = str(uuid.uuid4())
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Create user with starter tokens
            starter_tokens = 100  # Free starter tokens
            cursor.execute('''
                INSERT INTO users (user_id, email, tier, token_balance, password_hash)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, email, tier, starter_tokens, password_hash))
            
            # Log the starter token grant
            cursor.execute('''
                INSERT INTO transactions (user_id, transaction_type, amount, description)
                VALUES (?, 'credit', ?, 'Welcome bonus tokens')
            ''', (user_id, starter_tokens))
            
            # Create subscription
            expires_at = datetime.now() + timedelta(days=30)
            cursor.execute('''
                INSERT INTO subscriptions (user_id, tier, expires_at)
                VALUES (?, ?, ?)
            ''', (user_id, tier, expires_at))
            
            conn.commit()
            
            return {
                'success': True,
                'user_id': user_id,
                'email': email,
                'tier': tier,
                'token_balance': starter_tokens,
                'message': f'Account created successfully! {starter_tokens} welcome tokens added.'
            }
            
        except sqlite3.IntegrityError:
            return {
                'success': False,
                'error': 'Email already exists'
            }
        finally:
            conn.close()
    
    def authenticate_user(self, email: str, password: str) -> Dict:
        """Authenticate user and return JWT token"""
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT user_id, email, tier, token_balance, total_spent
            FROM users WHERE email = ? AND password_hash = ?
        ''', (email, password_hash))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            user_id, email, tier, token_balance, total_spent = user
            
            # Generate JWT token
            payload = {
                'user_id': user_id,
                'email': email,
                'tier': tier,
                'exp': datetime.utcnow() + timedelta(hours=24)
            }
            token = jwt.encode(payload, self.secret_key, algorithm='HS256')
            
            return {
                'success': True,
                'token': token,
                'user': {
                    'user_id': user_id,
                    'email': email,
                    'tier': tier,
                    'token_balance': token_balance,
                    'total_spent': total_spent
                }
            }
        else:
            return {
                'success': False,
                'error': 'Invalid credentials'
            }
    
    def verify_token(self, token: str) -> Optional[Dict]:
        """Verify JWT token and return user info"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
    
    def get_user_balance(self, user_id: str) -> int:
        """Get user's current token balance"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT token_balance FROM users WHERE user_id = ?', (user_id,))
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result else 0
    
    def deduct_tokens(self, user_id: str, amount: int, description: str, job_id: str = None) -> Dict:
        """Deduct tokens from user account"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Check current balance
        cursor.execute('SELECT token_balance FROM users WHERE user_id = ?', (user_id,))
        current_balance = cursor.fetchone()[0]
        
        if current_balance < amount:
            conn.close()
            return {
                'success': False,
                'error': 'Insufficient token balance',
                'required': amount,
                'available': current_balance
            }
        
        # Deduct tokens
        new_balance = current_balance - amount
        cursor.execute('''
            UPDATE users SET token_balance = ?, total_spent = total_spent + ?
            WHERE user_id = ?
        ''', (new_balance, amount, user_id))
        
        # Log transaction
        cursor.execute('''
            INSERT INTO transactions (user_id, transaction_type, amount, description, metadata)
            VALUES (?, 'debit', ?, ?, ?)
        ''', (user_id, amount, description, job_id))
        
        conn.commit()
        conn.close()
        
        return {
            'success': True,
            'new_balance': new_balance,
            'deducted': amount
        }
    
    def add_tokens(self, user_id: str, amount: int, description: str) -> Dict:
        """Add tokens to user account"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE users SET token_balance = token_balance + ?
            WHERE user_id = ?
        ''', (amount, user_id))
        
        # Log transaction
        cursor.execute('''
            INSERT INTO transactions (user_id, transaction_type, amount, description)
            VALUES (?, 'credit', ?, ?)
        ''', (user_id, amount, description))
        
        # Get new balance
        cursor.execute('SELECT token_balance FROM users WHERE user_id = ?', (user_id,))
        new_balance = cursor.fetchone()[0]
        
        conn.commit()
        conn.close()
        
        return {
            'success': True,
            'new_balance': new_balance,
            'added': amount
        }
    
    def purchase_tokens(self, user_id: str, package_name: str, payment_method: str = 'stripe') -> Dict:
        """Process token purchase"""
        if package_name not in TOKEN_PACKAGES:
            return {
                'success': False,
                'error': 'Invalid package'
            }
        
        package = TOKEN_PACKAGES[package_name]
        total_tokens = package['tokens'] + package['bonus']
        
        # In a real implementation, you would process payment here
        # For demo purposes, we'll simulate successful payment
        transaction_id = f"txn_{uuid.uuid4().hex[:8]}"
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Add tokens to user account
        cursor.execute('''
            UPDATE users SET token_balance = token_balance + ?
            WHERE user_id = ?
        ''', (total_tokens, user_id))
        
        # Log purchase
        cursor.execute('''
            INSERT INTO token_purchases 
            (user_id, package_name, tokens_purchased, bonus_tokens, amount_paid, payment_method, transaction_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (user_id, package_name, package['tokens'], package['bonus'], 
              package['price'], payment_method, transaction_id))
        
        # Log transaction
        cursor.execute('''
            INSERT INTO transactions (user_id, transaction_type, amount, description, metadata)
            VALUES (?, 'credit', ?, ?, ?)
        ''', (user_id, total_tokens, f'Token purchase: {package_name}', transaction_id))
        
        # Get new balance
        cursor.execute('SELECT token_balance FROM users WHERE user_id = ?', (user_id,))
        new_balance = cursor.fetchone()[0]
        
        conn.commit()
        conn.close()
        
        return {
            'success': True,
            'transaction_id': transaction_id,
            'tokens_added': total_tokens,
            'new_balance': new_balance,
            'amount_paid': package['price']
        }
    
    def get_user_usage_stats(self, user_id: str, days: int = 30) -> Dict:
        """Get user usage statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        start_date = datetime.now() - timedelta(days=days)
        
        # Total tokens spent
        cursor.execute('''
            SELECT SUM(amount) FROM transactions 
            WHERE user_id = ? AND transaction_type = 'debit' AND created_at > ?
        ''', (user_id, start_date))
        tokens_spent = cursor.fetchone()[0] or 0
        
        # Training jobs
        cursor.execute('''
            SELECT COUNT(*), SUM(tokens_consumed) FROM usage_logs 
            WHERE user_id = ? AND service_type = 'training' AND created_at > ?
        ''', (user_id, start_date))
        training_stats = cursor.fetchone()
        training_jobs = training_stats[0] or 0
        training_tokens = training_stats[1] or 0
        
        # Inference requests
        cursor.execute('''
            SELECT COUNT(*), SUM(tokens_consumed) FROM usage_logs 
            WHERE user_id = ? AND service_type = 'inference' AND created_at > ?
        ''', (user_id, start_date))
        inference_stats = cursor.fetchone()
        inference_requests = inference_stats[0] or 0
        inference_tokens = inference_stats[1] or 0
        
        conn.close()
        
        return {
            'period_days': days,
            'total_tokens_spent': tokens_spent,
            'training': {
                'jobs': training_jobs,
                'tokens': training_tokens
            },
            'inference': {
                'requests': inference_requests,
                'tokens': inference_tokens
            },
            'average_daily_usage': tokens_spent / days if days > 0 else 0
        }
    
    def create_api_key(self, user_id: str, name: str, permissions: List[str] = None) -> Dict:
        """Create API key for user"""
        if permissions is None:
            permissions = ['training', 'inference']
        
        key_id = str(uuid.uuid4())
        api_key = f"lts_{uuid.uuid4().hex}"
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO api_keys (key_id, user_id, api_key, name, permissions)
            VALUES (?, ?, ?, ?, ?)
        ''', (key_id, user_id, api_key, name, ','.join(permissions)))
        
        conn.commit()
        conn.close()
        
        return {
            'success': True,
            'key_id': key_id,
            'api_key': api_key,
            'name': name,
            'permissions': permissions
        }
