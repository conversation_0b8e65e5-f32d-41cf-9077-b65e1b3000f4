#!/usr/bin/env python3
"""
Simple startup script for the AI Toolkit Flask Application
This script handles dependency issues and starts the Flask app
"""

import os
import sys
import subprocess

def fix_numpy_compatibility():
    """Fix numpy compatibility issues"""
    try:
        import numpy
        print(f"Current numpy version: {numpy.__version__}")
        
        # Check if we have the compatibility issue
        try:
            import tensorflow
            print("TensorFlow imported successfully")
        except Exception as e:
            if "module compiled against API version" in str(e):
                print("Detected numpy compatibility issue. Attempting to fix...")
                
                # Try to reinstall numpy with compatible version
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", 
                    "numpy>=1.21.0,<1.25.0", "--force-reinstall"
                ])
                print("Numpy reinstalled with compatible version")
                
                # Also try to fix tensorflow if needed
                try:
                    subprocess.check_call([
                        sys.executable, "-m", "pip", "install", 
                        "tensorflow>=2.12.0,<2.16.0", "--force-reinstall"
                    ])
                    print("TensorFlow reinstalled with compatible version")
                except:
                    print("Could not reinstall TensorFlow, but app should still work for basic functionality")
            else:
                print(f"Different import error: {e}")
                
    except ImportError:
        print("Numpy not installed, installing...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "numpy>=1.21.0,<1.25.0"
        ])

def install_basic_requirements():
    """Install basic requirements for the Flask app"""
    basic_requirements = [
        "flask==3.0.0",
        "flask-cors==4.0.0", 
        "werkzeug==3.0.1",
        "pillow>=10.0.0",
        "pyyaml",
        "python-slugify"
    ]
    
    for req in basic_requirements:
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", req
            ])
        except subprocess.CalledProcessError:
            print(f"Warning: Could not install {req}")

def main():
    print("🚀 Starting AI Toolkit Flask Application")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python version: {sys.version}")
    
    # Install basic requirements
    print("📦 Installing basic requirements...")
    install_basic_requirements()
    
    # Fix numpy compatibility
    print("🔧 Checking numpy compatibility...")
    try:
        fix_numpy_compatibility()
        print("✅ Numpy compatibility checked")
    except Exception as e:
        print(f"⚠️  Warning: Could not fix numpy compatibility: {e}")
        print("   The app will still work for basic functionality")
    
    # Check if app.py exists
    if not os.path.exists('app.py'):
        print("❌ app.py not found in current directory")
        print("   Please run this script from the ai-toolkit-main directory")
        sys.exit(1)
    
    print("🌐 Starting Flask application...")
    print("📱 The interface will be available at: http://localhost:5001")
    print("🛑 Press Ctrl+C to stop the server")
    print()
    
    # Start the Flask app
    try:
        # Import and run the app
        sys.path.insert(0, os.getcwd())
        
        # Try to import the app
        try:
            from app import app
            print("✅ Flask app imported successfully")
            
            # Run the app
            app.run(debug=True, host='0.0.0.0', port=5001)
            
        except ImportError as e:
            print(f"❌ Error importing Flask app: {e}")
            print("   Trying to run with subprocess...")
            
            # Fallback: run with subprocess
            subprocess.run([sys.executable, 'app.py'])
            
    except KeyboardInterrupt:
        print("\n👋 Shutting down Flask application")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        print("\n💡 Troubleshooting tips:")
        print("   1. Make sure you're in the ai-toolkit-main directory")
        print("   2. Try running: pip install flask flask-cors pillow pyyaml python-slugify")
        print("   3. For AI captioning, install: pip install torch transformers")
        sys.exit(1)

if __name__ == '__main__':
    main()
