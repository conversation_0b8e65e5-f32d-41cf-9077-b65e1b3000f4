#!/usr/bin/env python3
"""
Test script for the AI Toolkit Flask Application
"""

import requests
import json
import time
import os
from pathlib import Path

BASE_URL = "http://localhost:5000"

def test_api_endpoint(endpoint, method="GET", data=None, files=None):
    """Test an API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            if files:
                response = requests.post(url, data=data, files=files)
            else:
                response = requests.post(url, json=data)
        else:
            print(f"❌ Unsupported method: {method}")
            return False
        
        if response.status_code == 200:
            print(f"✅ {method} {endpoint} - OK")
            return True
        else:
            print(f"❌ {method} {endpoint} - Status: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   Response: {response.text[:100]}...")
            return False
    
    except requests.exceptions.ConnectionError:
        print(f"❌ {method} {endpoint} - Connection failed (is the server running?)")
        return False
    except Exception as e:
        print(f"❌ {method} {endpoint} - Error: {e}")
        return False

def test_configs():
    """Test configuration endpoints"""
    print("\n🔧 Testing Configuration Endpoints")
    print("-" * 40)
    
    # Test getting all configs
    success = test_api_endpoint("/api/configs")
    
    if success:
        # Get config details
        try:
            response = requests.get(f"{BASE_URL}/api/configs")
            configs = response.json()
            
            if configs:
                # Test getting specific config
                first_config = list(configs.keys())[0]
                test_api_endpoint(f"/api/config/{first_config}")
                print(f"   📋 Found config: {first_config}")
            else:
                print("   ⚠️  No configurations found")
        except Exception as e:
            print(f"   ❌ Error parsing configs: {e}")

def test_training_status():
    """Test training status endpoint"""
    print("\n📊 Testing Training Status")
    print("-" * 40)
    
    success = test_api_endpoint("/api/training/status")
    
    if success:
        try:
            response = requests.get(f"{BASE_URL}/api/training/status")
            status = response.json()
            print(f"   📈 Training status: {'Active' if status.get('is_training') else 'Idle'}")
            print(f"   📝 Message: {status.get('message', 'No message')}")
        except Exception as e:
            print(f"   ❌ Error parsing status: {e}")

def test_file_upload():
    """Test file upload (requires a test image)"""
    print("\n📁 Testing File Upload")
    print("-" * 40)
    
    # Create a simple test image if it doesn't exist
    test_image_path = "test_image.jpg"
    
    if not os.path.exists(test_image_path):
        try:
            from PIL import Image
            import numpy as np
            
            # Create a simple test image
            img_array = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
            img = Image.fromarray(img_array)
            img.save(test_image_path)
            print(f"   📷 Created test image: {test_image_path}")
        except ImportError:
            print("   ⚠️  PIL not available, skipping file upload test")
            return
        except Exception as e:
            print(f"   ❌ Error creating test image: {e}")
            return
    
    # Test file upload
    try:
        with open(test_image_path, 'rb') as f:
            files = {'files': f}
            success = test_api_endpoint("/upload", method="POST", files=files)
            
        if success:
            print("   ✅ File upload successful")
        
        # Clean up test image
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
            
    except Exception as e:
        print(f"   ❌ Error testing file upload: {e}")

def test_main_page():
    """Test the main page"""
    print("\n🏠 Testing Main Page")
    print("-" * 40)
    
    try:
        response = requests.get(BASE_URL)
        if response.status_code == 200:
            print("✅ Main page accessible")
            if "LoRA Training" in response.text:
                print("   📄 Page content looks correct")
            else:
                print("   ⚠️  Page content might be incorrect")
        else:
            print(f"❌ Main page - Status: {response.status_code}")
    except Exception as e:
        print(f"❌ Main page - Error: {e}")

def check_server_running():
    """Check if the Flask server is running"""
    try:
        response = requests.get(BASE_URL, timeout=5)
        return True
    except:
        return False

def main():
    print("🧪 AI Toolkit Flask Application Test Suite")
    print("=" * 50)
    
    # Check if server is running
    if not check_server_running():
        print("❌ Flask server is not running!")
        print("💡 Start the server with: python app.py")
        print("   Or use: python start_flask_app.py")
        return
    
    print("✅ Flask server is running")
    
    # Run tests
    test_main_page()
    test_configs()
    test_training_status()
    test_file_upload()
    
    print("\n📋 Test Summary")
    print("-" * 40)
    print("✅ Basic functionality tests completed")
    print("💡 For full testing, try the web interface at:")
    print(f"   {BASE_URL}")
    
    print("\n🔧 Manual Testing Checklist:")
    print("   □ Upload images/videos")
    print("   □ Select a configuration")
    print("   □ Generate captions")
    print("   □ Set training parameters")
    print("   □ Start training (if you have a dataset)")

if __name__ == '__main__':
    main()
