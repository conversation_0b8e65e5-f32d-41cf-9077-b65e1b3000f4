config:
  name: first
  process:
  - datasets:
    - cache_latents_to_disk: true
      caption_dropout_rate: 0.05
      caption_ext: txt
      folder_path: datasets/d3783517-54d3-46e1-b38b-b1e9a661d818
      resolution:
      - 512
      - 768
      - 1024
      shuffle_tokens: false
    device: cuda:0
    model:
      is_flux: true
      name_or_path: black-forest-labs/FLUX.1-dev
      quantize: true
    network:
      linear: 16
      linear_alpha: 16
      type: lora
    sample:
      guidance_scale: 4
      height: 1024
      neg: ''
      prompts:
      - indian girl standing
      - indian girl standing, high quality, detailed
      sample_every: 250
      sample_steps: 20
      sampler: flowmatch
      seed: 42
      walk_seed: true
      width: 1024
    save:
      dtype: float16
      max_step_saves_to_keep: 4
      save_every: 250
    train:
      batch_size: 1
      dtype: bf16
      ema_config:
        ema_decay: 0.99
        use_ema: true
      gradient_accumulation_steps: 1
      gradient_checkpointing: true
      lr: 0.001
      noise_scheduler: flowmatch
      optimizer: adamw8bit
      steps: 1000
      train_text_encoder: false
      train_unet: true
    training_folder: /root/ai-toolkit/modal_output
    type: sd_trainer
job: extension
meta:
  name: '[name]'
  version: '1.0'
