import os
import sys
import uuid
import yaml
import shutil
import subprocess
import threading
from pathlib import Path

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename
from PIL import Image
import torch
from transformers import AutoProcessor, AutoModelForCausalLM
from slugify import slugify

# Add the current working directory to the Python path
sys.path.insert(0, os.getcwd())

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
DATASET_FOLDER = 'datasets'
OUTPUT_FOLDER = 'output'
CONFIG_FOLDER = 'config'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'txt'}
MAX_CONTENT_LENGTH = 500 * 1024 * 1024  # 500MB max file size

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Create necessary directories
for folder in [UPLOAD_FOLDER, DATASET_FOLDER, OUTPUT_FOLDER]:
    os.makedirs(folder, exist_ok=True)

# Global training status
training_status = {
    'is_training': False,
    'progress': 0,
    'message': 'Ready',
    'job_id': None,
    'error': None
}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS



def get_available_configs():
    """Get all available YAML config files"""
    configs = {}
    config_path = Path(CONFIG_FOLDER)

    if not config_path.exists():
        return configs

    for config_file in config_path.glob('*.yaml'):
        try:
            with open(config_file, 'r') as f:
                config_data = yaml.safe_load(f)
                configs[config_file.stem] = {
                    'file': str(config_file),
                    'name': config_file.stem,
                    'data': config_data
                }
        except Exception as e:
            print(f"Error loading config {config_file}: {e}")

    return configs

@app.route('/')
def index():
    """Main page with upload and configuration interface"""
    configs = get_available_configs()
    return render_template('index.html', configs=configs)

@app.route('/api/configs')
def api_configs():
    """API endpoint to get available configurations"""
    configs = get_available_configs()
    return jsonify(configs)

@app.route('/api/config/<config_name>')
def api_config_details(config_name):
    """API endpoint to get detailed configuration"""
    try:
        config_file = Path(CONFIG_FOLDER) / f"{config_name}.yaml"
        if not config_file.exists():
            return jsonify({'error': 'Config not found'}), 404

        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)

        return jsonify({
            'name': config_name,
            'data': config_data
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/upload', methods=['POST'])
def upload_files():
    """Handle file uploads"""
    if 'files' not in request.files:
        return jsonify({'error': 'No files provided'}), 400
    
    files = request.files.getlist('files')
    if not files or files[0].filename == '':
        return jsonify({'error': 'No files selected'}), 400
    
    # Create unique upload session
    session_id = str(uuid.uuid4())
    session_folder = os.path.join(UPLOAD_FOLDER, session_id)
    os.makedirs(session_folder, exist_ok=True)
    
    uploaded_files = []
    
    for file in files:
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            file_path = os.path.join(session_folder, filename)
            file.save(file_path)
            
            # Get file info
            file_info = {
                'filename': filename,
                'path': file_path,
                'size': os.path.getsize(file_path),
                'type': 'image' if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')) else 'video' if filename.lower().endswith(('.mp4', '.avi', '.mov')) else 'text'
            }
            
            # If it's an image, get dimensions
            if file_info['type'] == 'image':
                try:
                    with Image.open(file_path) as img:
                        file_info['width'], file_info['height'] = img.size
                except Exception as e:
                    print(f"Error getting image dimensions: {e}")
            
            uploaded_files.append(file_info)
    
    return jsonify({
        'session_id': session_id,
        'files': uploaded_files,
        'message': f'Successfully uploaded {len(uploaded_files)} files'
    })

@app.route('/api/caption', methods=['POST'])
def auto_caption():
    """Generate captions for uploaded images"""
    data = request.get_json()
    session_id = data.get('session_id')
    concept_sentence = data.get('concept_sentence', '')
    
    if not session_id:
        return jsonify({'error': 'Session ID required'}), 400
    
    session_folder = os.path.join(UPLOAD_FOLDER, session_id)
    if not os.path.exists(session_folder):
        return jsonify({'error': 'Session not found'}), 404
    
    try:
        # Load Florence-2 model for captioning
        device = "cuda" if torch.cuda.is_available() else "cpu"
        torch_dtype = torch.float16
        
        model = AutoModelForCausalLM.from_pretrained(
            "multimodalart/Florence-2-large-no-flash-attn", 
            torch_dtype=torch_dtype, 
            trust_remote_code=True
        ).to(device)
        
        processor = AutoProcessor.from_pretrained(
            "multimodalart/Florence-2-large-no-flash-attn", 
            trust_remote_code=True
        )
        
        captions = {}
        
        # Process each image file
        for filename in os.listdir(session_folder):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                image_path = os.path.join(session_folder, filename)
                
                try:
                    image = Image.open(image_path).convert("RGB")
                    
                    prompt = "<DETAILED_CAPTION>"
                    inputs = processor(text=prompt, images=image, return_tensors="pt").to(device, torch_dtype)
                    
                    generated_ids = model.generate(
                        input_ids=inputs["input_ids"], 
                        pixel_values=inputs["pixel_values"], 
                        max_new_tokens=1024, 
                        num_beams=3
                    )
                    
                    generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]
                    parsed_answer = processor.post_process_generation(
                        generated_text, task=prompt, image_size=(image.width, image.height)
                    )
                    
                    caption_text = parsed_answer["<DETAILED_CAPTION>"].replace("The image shows ", "")
                    
                    if concept_sentence:
                        caption_text = f"{caption_text} [trigger]"
                    
                    captions[filename] = caption_text
                    
                except Exception as e:
                    print(f"Error captioning {filename}: {e}")
                    captions[filename] = f"Error generating caption: {str(e)}"
        
        # Clean up model
        model.to("cpu")
        del model
        del processor
        torch.cuda.empty_cache()
        
        return jsonify({
            'captions': captions,
            'message': f'Generated captions for {len(captions)} images'
        })
        
    except Exception as e:
        return jsonify({'error': f'Captioning failed: {str(e)}'}), 500

def create_dataset_folder(session_folder, captions, concept_sentence=''):
    """Create a dataset folder from uploaded files and captions"""
    dataset_id = str(uuid.uuid4())
    dataset_path = os.path.join(DATASET_FOLDER, dataset_id)
    os.makedirs(dataset_path, exist_ok=True)

    image_count = 0
    for filename in os.listdir(session_folder):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
            # Copy image
            src_path = os.path.join(session_folder, filename)
            dst_path = os.path.join(dataset_path, filename)
            shutil.copy2(src_path, dst_path)

            # Create caption file
            caption_filename = os.path.splitext(filename)[0] + '.txt'
            caption_path = os.path.join(dataset_path, caption_filename)

            caption_text = captions.get(filename, '[trigger]' if concept_sentence else 'a photo')

            # Replace [trigger] with actual concept sentence
            if concept_sentence and '[trigger]' in caption_text:
                caption_text = caption_text.replace('[trigger]', concept_sentence)

            with open(caption_path, 'w') as f:
                f.write(caption_text)

            image_count += 1

    if image_count == 0:
        raise ValueError("No images found in session folder")

    return dataset_path

def prepare_training_config(base_config_path, dataset_path, lora_name, concept_sentence='', **params):
    """Prepare a training configuration file"""
    with open(base_config_path, 'r') as f:
        config = yaml.safe_load(f)

    # Update config with user parameters
    slugged_name = slugify(lora_name)
    config['config']['name'] = slugged_name

    # Update process configuration
    process_config = config['config']['process'][0]

    # Update dataset path
    if 'datasets' in process_config and len(process_config['datasets']) > 0:
        process_config['datasets'][0]['folder_path'] = dataset_path

    # Update training parameters if provided
    if 'train' in process_config:
        train_config = process_config['train']
        if 'steps' in params:
            train_config['steps'] = int(params['steps'])
        if 'learning_rate' in params:
            train_config['lr'] = float(params['learning_rate'])
        if 'batch_size' in params:
            train_config['batch_size'] = int(params['batch_size'])

    # Update network parameters if provided
    if 'network' in process_config and 'rank' in params:
        network_config = process_config['network']
        rank = int(params['rank'])
        network_config['linear'] = rank
        network_config['linear_alpha'] = rank

    # Set trigger word
    if concept_sentence:
        process_config['trigger_word'] = concept_sentence

    # Save modified config
    job_id = str(uuid.uuid4())
    config_path = os.path.join('tmp', f"{job_id}-{slugged_name}.yaml")
    os.makedirs('tmp', exist_ok=True)

    with open(config_path, 'w') as f:
        yaml.dump(config, f)

    return config_path, job_id

@app.route('/api/train', methods=['POST'])
def start_training():
    """Start LoRA training with selected configuration"""
    global training_status

    if training_status['is_training']:
        return jsonify({'error': 'Training already in progress'}), 400

    data = request.get_json()

    # Extract required parameters
    session_id = data.get('session_id')
    config_name = data.get('config_name')
    lora_name = data.get('lora_name')
    concept_sentence = data.get('concept_sentence', '')
    captions = data.get('captions', {})

    # Validation
    if not all([session_id, config_name, lora_name]):
        return jsonify({'error': 'Missing required parameters: session_id, config_name, lora_name'}), 400

    session_folder = os.path.join(UPLOAD_FOLDER, session_id)
    if not os.path.exists(session_folder):
        return jsonify({'error': 'Session not found'}), 404

    try:
        # Create dataset
        dataset_path = create_dataset_folder(session_folder, captions, concept_sentence)

        # Get base config path
        base_config_path = os.path.join(CONFIG_FOLDER, f"{config_name}.yaml")
        if not os.path.exists(base_config_path):
            return jsonify({'error': 'Configuration file not found'}), 404

        # Prepare training configuration
        config_path, job_id = prepare_training_config(
            base_config_path,
            dataset_path,
            lora_name,
            concept_sentence,
            steps=data.get('steps', 1000),
            learning_rate=data.get('learning_rate', 1e-4),
            rank=data.get('rank', 16),
            batch_size=data.get('batch_size', 1)
        )

        # Update training status
        training_status.update({
            'is_training': True,
            'progress': 0,
            'message': 'Starting training...',
            'job_id': job_id,
            'error': None
        })

        # Start training in background thread using the existing run.py command
        def run_training():
            try:
                # Use subprocess to run the existing training command
                cmd = [sys.executable, 'run.py', config_path]
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=os.getcwd()
                )

                # Wait for completion
                stdout, stderr = process.communicate()

                if process.returncode == 0:
                    training_status.update({
                        'is_training': False,
                        'progress': 100,
                        'message': f'Training completed successfully. Model saved as {slugify(lora_name)}',
                        'error': None
                    })
                else:
                    training_status.update({
                        'is_training': False,
                        'progress': 0,
                        'message': 'Training failed',
                        'error': stderr or 'Unknown error'
                    })

            except Exception as e:
                training_status.update({
                    'is_training': False,
                    'progress': 0,
                    'message': 'Training failed',
                    'error': str(e)
                })

        training_thread = threading.Thread(target=run_training)
        training_thread.daemon = True
        training_thread.start()

        return jsonify({
            'job_id': job_id,
            'message': 'Training started successfully',
            'dataset_path': dataset_path,
            'config_path': config_path
        })

    except Exception as e:
        return jsonify({'error': f'Failed to start training: {str(e)}'}), 500

@app.route('/api/training/status')
def get_training_status():
    """Get current training status"""
    return jsonify(training_status)

@app.route('/api/training/stop', methods=['POST'])
def stop_training():
    """Stop current training"""
    global training_status

    if not training_status['is_training']:
        return jsonify({'error': 'No training in progress'}), 400

    # Simple implementation - just update status
    # In a real scenario, you'd need to kill the subprocess
    training_status.update({
        'is_training': False,
        'progress': 0,
        'message': 'Training stopped by user',
        'error': None
    })

    return jsonify({'message': 'Training stop requested'})

@app.route('/uploads/<session_id>/<filename>')
def uploaded_file(session_id, filename):
    """Serve uploaded files"""
    return send_from_directory(os.path.join(UPLOAD_FOLDER, session_id), filename)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
