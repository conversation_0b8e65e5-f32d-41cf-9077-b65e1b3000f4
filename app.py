import os
import sys
import uuid
import yaml
import shutil
import subprocess
import threading
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path

from flask import Flask, render_template, request, jsonify, send_from_directory, send_file
from flask_cors import CORS
from werkzeug.utils import secure_filename
from PIL import Image
from slugify import slugify
from functools import wraps

# Add the current working directory to the Python path
sys.path.insert(0, os.getcwd())

# Import billing and auth systems
try:
    from billing_manager import BillingManager
    from pricing_engine import TokenPricingEngine, ServiceTier, TOKEN_PACKAGES
    from auth_system import AdvancedAuthSystem
    BILLING_ENABLED = True
except ImportError as e:
    print(f"Billing system not available: {e}")
    BILLING_ENABLED = False

# Global variables for AI models (loaded only when needed)
florence_model = None
florence_processor = None

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
DATASET_FOLDER = 'datasets'
OUTPUT_FOLDER = 'output'
CONFIG_FOLDER = 'config'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'txt'}
MAX_CONTENT_LENGTH = 500 * 1024 * 1024  # 500MB max file size

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Create necessary directories
for folder in [UPLOAD_FOLDER, DATASET_FOLDER, OUTPUT_FOLDER]:
    os.makedirs(folder, exist_ok=True)

# Initialize billing and auth systems
billing_manager = None
pricing_engine = None
auth_system = None

if BILLING_ENABLED:
    billing_manager = BillingManager()
    pricing_engine = TokenPricingEngine()
    auth_system = AdvancedAuthSystem()
    print("✅ Billing and authentication systems initialized")

# Global training status
training_status = {
    'is_training': False,
    'progress': 0,
    'message': 'Ready',
    'job_id': None,
    'error': None
}

def require_auth(f):
    """Decorator to require authentication for API endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not BILLING_ENABLED:
            return f(*args, **kwargs)  # Skip auth if billing disabled

        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'error': 'Authentication required'}), 401

        token = auth_header.split(' ')[1]
        user_info = billing_manager.verify_token(token)

        if not user_info:
            return jsonify({'error': 'Invalid or expired token'}), 401

        # Add user info to request context
        request.user = user_info
        return f(*args, **kwargs)

    return decorated_function

def check_token_balance(required_tokens):
    """Decorator to check if user has sufficient tokens"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not BILLING_ENABLED:
                return f(*args, **kwargs)  # Skip check if billing disabled

            user_id = request.user['user_id']
            balance = billing_manager.get_user_balance(user_id)

            if balance < required_tokens:
                return jsonify({
                    'error': 'Insufficient token balance',
                    'required': required_tokens,
                    'available': balance
                }), 402  # Payment Required

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS



def get_available_configs():
    """Get all available YAML config files"""
    configs = {}
    config_path = Path(CONFIG_FOLDER)

    if not config_path.exists():
        return configs

    for config_file in config_path.glob('*.yaml'):
        try:
            with open(config_file, 'r') as f:
                config_data = yaml.safe_load(f)
                configs[config_file.stem] = {
                    'file': str(config_file),
                    'name': config_file.stem,
                    'data': config_data
                }
        except Exception as e:
            print(f"Error loading config {config_file}: {e}")

    return configs

@app.route('/')
def home():
    """Professional home page with authentication"""
    return render_template('home.html')

@app.route('/training')
def training():
    """Main training interface (requires authentication)"""
    return render_template('modern.html')

@app.route('/pricing')
def pricing():
    """Professional pricing page"""
    return render_template('pricing.html')

@app.route('/test')
def test():
    """Simple test endpoint"""
    return jsonify({
        'status': 'working',
        'message': 'Flask app is running correctly',
        'configs_found': len(get_available_configs()),
        'billing_enabled': BILLING_ENABLED
    })

# Billing API Endpoints
@app.route('/auth')
def auth_page():
    """Professional authentication page"""
    return render_template('auth.html')

@app.route('/api/auth/register', methods=['POST'])
def register():
    """Register a new user with email verification"""
    if not BILLING_ENABLED:
        return jsonify({'error': 'Billing system not available'}), 503

    data = request.get_json()
    email = data.get('email')
    password = data.get('password')
    full_name = data.get('full_name', '')
    tier = data.get('tier', 'starter')

    if not email or not password:
        return jsonify({'error': 'Email and password required'}), 400

    # Create user account
    result = billing_manager.create_user(email, password, tier)

    if result['success']:
        # Send verification email
        verification_result = auth_system.send_verification_email(email, full_name)

        # Update user with full name
        if full_name:
            conn = sqlite3.connect(billing_manager.db_path)
            cursor = conn.cursor()
            cursor.execute('UPDATE users SET full_name = ? WHERE email = ?', (full_name, email))
            conn.commit()
            conn.close()

        return jsonify({
            'success': True,
            'message': 'Account created! Please check your email for verification code.',
            'verification_sent': verification_result['success']
        })

    return jsonify(result)

@app.route('/api/auth/login', methods=['POST'])
def login():
    """Authenticate user and return JWT token"""
    if not BILLING_ENABLED:
        return jsonify({'error': 'Billing system not available'}), 503

    data = request.get_json()
    email = data.get('email')
    password = data.get('password')

    if not email or not password:
        return jsonify({'error': 'Email and password required'}), 400

    result = billing_manager.authenticate_user(email, password)
    return jsonify(result)

@app.route('/api/auth/verify-email', methods=['POST'])
def verify_email():
    """Verify email with code"""
    if not BILLING_ENABLED:
        return jsonify({'error': 'Billing system not available'}), 503

    data = request.get_json()
    email = data.get('email')
    code = data.get('code')

    if not email or not code:
        return jsonify({'error': 'Email and code required'}), 400

    result = auth_system.verify_email_code(email, code)
    return jsonify(result)

@app.route('/api/auth/resend-verification', methods=['POST'])
def resend_verification():
    """Resend verification email"""
    if not BILLING_ENABLED:
        return jsonify({'error': 'Billing system not available'}), 503

    data = request.get_json()
    email = data.get('email')

    if not email:
        return jsonify({'error': 'Email required'}), 400

    # Get user name for personalized email
    conn = sqlite3.connect(billing_manager.db_path)
    cursor = conn.cursor()
    cursor.execute('SELECT full_name FROM users WHERE email = ?', (email,))
    result = cursor.fetchone()
    user_name = result[0] if result else ''
    conn.close()

    result = auth_system.send_verification_email(email, user_name)
    return jsonify(result)

@app.route('/api/auth/forgot-password', methods=['POST'])
def forgot_password():
    """Send password reset email"""
    if not BILLING_ENABLED:
        return jsonify({'error': 'Billing system not available'}), 503

    data = request.get_json()
    email = data.get('email')

    if not email:
        return jsonify({'error': 'Email required'}), 400

    # Check if user exists
    conn = sqlite3.connect(billing_manager.db_path)
    cursor = conn.cursor()
    cursor.execute('SELECT user_id, full_name FROM users WHERE email = ?', (email,))
    user = cursor.fetchone()

    if user:
        user_id, user_name = user

        # Generate reset token
        import secrets
        reset_token = secrets.token_urlsafe(32)
        expires_at = datetime.now() + timedelta(hours=1)

        # Store reset token
        cursor.execute('''
            INSERT INTO password_resets (user_id, reset_token, expires_at)
            VALUES (?, ?, ?)
        ''', (user_id, reset_token, expires_at))

        conn.commit()

        # Send reset email
        success = auth_system.email_service.send_password_reset_email(email, reset_token, user_name or '')

        conn.close()

        return jsonify({
            'success': success,
            'message': 'Password reset email sent' if success else 'Failed to send reset email'
        })
    else:
        conn.close()
        # Don't reveal if email exists or not for security
        return jsonify({
            'success': True,
            'message': 'If the email exists, a reset link has been sent'
        })

@app.route('/api/auth/google/url')
def google_auth_url():
    """Get Google OAuth authorization URL"""
    if not BILLING_ENABLED:
        return jsonify({'error': 'Billing system not available'}), 503

    try:
        authorization_url, state = auth_system.google_auth.get_authorization_url()
        return jsonify({
            'authorization_url': authorization_url,
            'state': state
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/auth/google/callback')
def google_auth_callback():
    """Handle Google OAuth callback"""
    if not BILLING_ENABLED:
        return jsonify({'error': 'Billing system not available'}), 503

    code = request.args.get('code')
    state = request.args.get('state')

    if not code:
        return jsonify({'error': 'Authorization code required'}), 400

    try:
        # Exchange code for user info
        result = auth_system.google_auth.exchange_code_for_token(code, state)

        if result['success']:
            email = result['email']
            name = result['name']
            picture = result['picture']

            # Check if user exists
            conn = sqlite3.connect(billing_manager.db_path)
            cursor = conn.cursor()
            cursor.execute('SELECT user_id FROM users WHERE email = ?', (email,))
            existing_user = cursor.fetchone()

            if existing_user:
                # Login existing user
                user_id = existing_user[0]

                # Update OAuth info
                cursor.execute('''
                    INSERT OR REPLACE INTO oauth_accounts
                    (user_id, provider, provider_id, email, name, picture)
                    VALUES (?, 'google', ?, ?, ?, ?)
                ''', (user_id, email, email, name, picture))

                conn.commit()
                conn.close()

                # Generate JWT token
                auth_result = billing_manager.authenticate_user(email, None)  # OAuth login
                return render_template('oauth_success.html', token=auth_result.get('token'))

            else:
                # Create new user
                user_result = billing_manager.create_user(email, 'oauth_user', 'starter')

                if user_result['success']:
                    user_id = user_result['user_id']

                    # Update user info
                    cursor.execute('''
                        UPDATE users SET full_name = ?, profile_picture = ?, email_verified = TRUE
                        WHERE user_id = ?
                    ''', (name, picture, user_id))

                    # Store OAuth info
                    cursor.execute('''
                        INSERT INTO oauth_accounts
                        (user_id, provider, provider_id, email, name, picture)
                        VALUES (?, 'google', ?, ?, ?, ?)
                    ''', (user_id, email, email, name, picture))

                    conn.commit()
                    conn.close()

                    return render_template('oauth_success.html', message='Account created successfully!')
                else:
                    conn.close()
                    return jsonify({'error': 'Failed to create account'}), 500
        else:
            return jsonify({'error': result['error']}), 400

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/user/profile')
@require_auth
def get_profile():
    """Get user profile and token balance"""
    user_id = request.user['user_id']
    balance = billing_manager.get_user_balance(user_id)
    usage_stats = billing_manager.get_user_usage_stats(user_id)

    return jsonify({
        'user': request.user,
        'token_balance': balance,
        'usage_stats': usage_stats
    })

@app.route('/api/pricing/calculate', methods=['POST'])
@require_auth
def calculate_pricing():
    """Calculate training cost"""
    data = request.get_json()

    # Get user tier
    user_tier = ServiceTier(request.user.get('tier', 'starter'))

    # Calculate training cost
    cost_info = pricing_engine.calculate_training_cost(
        steps=data.get('steps', 1000),
        batch_size=data.get('batch_size', 1),
        rank=data.get('rank', 16),
        num_images=data.get('num_images', 10),
        model_type=data.get('model_type', 'flux'),
        user_tier=user_tier
    )

    return jsonify(cost_info)

@app.route('/api/pricing/tiers')
def get_pricing_tiers():
    """Get available pricing tiers"""
    if not BILLING_ENABLED:
        return jsonify({'error': 'Billing system not available'}), 503

    tiers = pricing_engine.get_pricing_tiers()
    return jsonify({'tiers': tiers})

@app.route('/api/tokens/packages')
def get_token_packages():
    """Get available token packages"""
    if not BILLING_ENABLED:
        return jsonify({'error': 'Billing system not available'}), 503

    return jsonify({'packages': TOKEN_PACKAGES})

@app.route('/api/tokens/purchase', methods=['POST'])
@require_auth
def purchase_tokens():
    """Purchase token package"""
    data = request.get_json()
    package_name = data.get('package_name')
    payment_method = data.get('payment_method', 'stripe')

    if not package_name:
        return jsonify({'error': 'Package name required'}), 400

    user_id = request.user['user_id']
    result = billing_manager.purchase_tokens(user_id, package_name, payment_method)

    return jsonify(result)

@app.route('/api/public/stats')
def public_stats():
    """Get public statistics for home page"""
    if not BILLING_ENABLED:
        return jsonify({
            'models_trained': 1000,
            'active_users': 500,
            'gpu_hours': 10000
        })

    conn = sqlite3.connect(billing_manager.db_path)
    cursor = conn.cursor()

    # Get basic stats
    cursor.execute('SELECT COUNT(*) FROM users')
    total_users = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(*) FROM usage_logs WHERE service_type = "training"')
    total_training_jobs = cursor.fetchone()[0]

    cursor.execute('SELECT SUM(duration_minutes) FROM usage_logs WHERE duration_minutes IS NOT NULL')
    total_minutes = cursor.fetchone()[0] or 0
    total_gpu_hours = int(total_minutes / 60)

    conn.close()

    return jsonify({
        'models_trained': max(total_training_jobs, 1000),
        'active_users': max(total_users, 500),
        'gpu_hours': max(total_gpu_hours, 10000)
    })

@app.route('/api/configs')
def api_configs():
    """API endpoint to get available configurations"""
    configs = get_available_configs()
    return jsonify(configs)

@app.route('/api/config/<config_name>')
def api_config_details(config_name):
    """API endpoint to get detailed configuration"""
    try:
        config_file = Path(CONFIG_FOLDER) / f"{config_name}.yaml"
        if not config_file.exists():
            return jsonify({'error': 'Config not found'}), 404

        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)

        return jsonify({
            'name': config_name,
            'data': config_data
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/upload', methods=['POST'])
def upload_files():
    """Handle file uploads"""
    if 'files' not in request.files:
        return jsonify({'error': 'No files provided'}), 400
    
    files = request.files.getlist('files')
    if not files or files[0].filename == '':
        return jsonify({'error': 'No files selected'}), 400
    
    # Create unique upload session
    session_id = str(uuid.uuid4())
    session_folder = os.path.join(UPLOAD_FOLDER, session_id)
    os.makedirs(session_folder, exist_ok=True)
    
    uploaded_files = []
    
    for file in files:
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            file_path = os.path.join(session_folder, filename)
            file.save(file_path)
            
            # Get file info
            file_info = {
                'filename': filename,
                'path': file_path,
                'size': os.path.getsize(file_path),
                'type': 'image' if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')) else 'video' if filename.lower().endswith(('.mp4', '.avi', '.mov')) else 'text'
            }
            
            # If it's an image, get dimensions
            if file_info['type'] == 'image':
                try:
                    with Image.open(file_path) as img:
                        file_info['width'], file_info['height'] = img.size
                except Exception as e:
                    print(f"Error getting image dimensions: {e}")
            
            uploaded_files.append(file_info)
    
    return jsonify({
        'session_id': session_id,
        'files': uploaded_files,
        'message': f'Successfully uploaded {len(uploaded_files)} files'
    })

class SimpleCaptionGenerator:
    """Professional BLIP-2 caption generator from captioning.ipynb"""

    def __init__(self, model_name="Salesforce/blip2-opt-2.7b"):
        """Initialize the caption generator with BLIP2"""
        # Import torch and transformers here
        import torch
        from transformers import Blip2Processor, Blip2ForConditionalGeneration

        self.torch = torch
        self.Blip2Processor = Blip2Processor
        self.Blip2ForConditionalGeneration = Blip2ForConditionalGeneration

        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model_name = model_name
        self.processor = None
        self.model = None
        print(f"Using device: {self.device}")

    def load_model(self):
        """Load BLIP2 model and processor"""
        print(f"Loading BLIP2 model: {self.model_name}")

        # Load processor
        self.processor = self.Blip2Processor.from_pretrained(self.model_name)

        # Load model
        self.model = self.Blip2ForConditionalGeneration.from_pretrained(
            self.model_name,
            torch_dtype=self.torch.float16 if self.device == "cuda" else self.torch.float32,
            device_map="auto" if self.device == "cuda" else None
        )

        if self.device == "cuda":
            self.model = self.model.to(self.device)

        print("✓ BLIP-2 model loaded successfully!")

    def generate_caption(self, image_path, prompt=None, max_length=100):
        """Generate caption for a single image"""
        # Load image
        image = Image.open(image_path)
        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Prepare inputs
        if prompt:
            inputs = self.processor(image, text=prompt, return_tensors="pt")
        else:
            inputs = self.processor(image, return_tensors="pt")

        if self.device == "cuda":
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

        # Generate caption
        with self.torch.no_grad():
            generated_ids = self.model.generate(
                **inputs,
                max_length=max_length,
                num_beams=5,
                temperature=0.7,
                do_sample=True,
                early_stopping=True
            )

        # Decode caption
        caption = self.processor.decode(generated_ids[0], skip_special_tokens=True)

        # Clean caption
        caption = caption.strip()
        if caption and not caption[0].isupper():
            caption = caption[0].upper() + caption[1:]
        if caption and caption[-1] not in '.!?':
            caption += '.'

        return caption

# Global caption generator instance
caption_generator = None

def get_caption_generator():
    """Get or create the global caption generator instance"""
    global caption_generator

    if caption_generator is None:
        try:
            caption_generator = SimpleCaptionGenerator()
            caption_generator.load_model()

        except ImportError as e:
            raise ImportError(f"Required packages not installed: {e}. Please install torch and transformers.")
        except Exception as e:
            raise Exception(f"Failed to load BLIP-2 model: {e}")

    return caption_generator

@app.route('/api/caption', methods=['POST'])
def auto_caption():
    """Generate captions for uploaded images"""
    data = request.get_json()
    session_id = data.get('session_id')
    concept_sentence = data.get('concept_sentence', '')

    if not session_id:
        return jsonify({'error': 'Session ID required'}), 400

    session_folder = os.path.join(UPLOAD_FOLDER, session_id)
    if not os.path.exists(session_folder):
        return jsonify({'error': 'Session not found'}), 404

    try:
        # Get the professional BLIP-2 caption generator from captioning.ipynb
        generator = get_caption_generator()

        captions = {}

        # Process each image file
        for filename in os.listdir(session_folder):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                image_path = os.path.join(session_folder, filename)

                try:
                    # Use the professional caption generator with enhanced prompts
                    prompt = None
                    if concept_sentence:
                        prompt = f"Describe this image in detail, including the concept '{concept_sentence}':"

                    # Generate high-quality caption using the notebook's implementation
                    caption_text = generator.generate_caption(
                        image_path,
                        prompt=prompt,
                        max_length=150
                    )

                    # Add trigger word if specified and not already present
                    if concept_sentence and "[trigger]" not in caption_text:
                        caption_text = f"{caption_text} [trigger]"

                    captions[filename] = caption_text

                except Exception as e:
                    print(f"Error captioning {filename}: {e}")
                    captions[filename] = f"Error generating caption: {str(e)}"

        return jsonify({
            'captions': captions,
            'message': f'Generated captions for {len(captions)} images'
        })

    except ImportError as e:
        return jsonify({'error': f'AI captioning not available: {str(e)}. You can still use manual captioning.'}), 500
    except Exception as e:
        return jsonify({'error': f'Captioning failed: {str(e)}'}), 500

def create_dataset_folder(session_folder, captions, concept_sentence=''):
    """Create a dataset folder from uploaded files and captions"""
    dataset_id = str(uuid.uuid4())
    dataset_path = os.path.join(DATASET_FOLDER, dataset_id)
    os.makedirs(dataset_path, exist_ok=True)

    image_count = 0
    for filename in os.listdir(session_folder):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
            # Copy image
            src_path = os.path.join(session_folder, filename)
            dst_path = os.path.join(dataset_path, filename)
            shutil.copy2(src_path, dst_path)

            # Create caption file
            caption_filename = os.path.splitext(filename)[0] + '.txt'
            caption_path = os.path.join(dataset_path, caption_filename)

            caption_text = captions.get(filename, '[trigger]' if concept_sentence else 'a photo')

            # Replace [trigger] with actual concept sentence
            if concept_sentence and '[trigger]' in caption_text:
                caption_text = caption_text.replace('[trigger]', concept_sentence)

            with open(caption_path, 'w') as f:
                f.write(caption_text)

            image_count += 1

    if image_count == 0:
        raise ValueError("No images found in session folder")

    return dataset_path

def prepare_training_config(base_config_path, dataset_path, lora_name, concept_sentence='', sample_prompt='', **params):
    """Prepare a training configuration file with enhanced sampling"""
    with open(base_config_path, 'r') as f:
        config = yaml.safe_load(f)

    # Update config with user parameters
    slugged_name = slugify(lora_name)
    config['config']['name'] = slugged_name

    # Update process configuration
    process_config = config['config']['process'][0]

    # Update dataset path
    if 'datasets' in process_config and len(process_config['datasets']) > 0:
        process_config['datasets'][0]['folder_path'] = dataset_path

    # Update training parameters if provided
    if 'train' in process_config:
        train_config = process_config['train']
        if 'steps' in params:
            train_config['steps'] = int(params['steps'])
        if 'learning_rate' in params:
            train_config['lr'] = float(params['learning_rate'])
        if 'batch_size' in params:
            train_config['batch_size'] = int(params['batch_size'])

    # Update network parameters if provided
    if 'network' in process_config and 'rank' in params:
        network_config = process_config['network']
        rank = int(params['rank'])
        network_config['linear'] = rank
        network_config['linear_alpha'] = rank

    # Enhanced sampling configuration
    if 'sample' in process_config:
        sample_config = process_config['sample']

        # Set custom sample prompt or use default
        if sample_prompt:
            # Use custom prompt with trigger word replacement
            if concept_sentence and '[trigger]' in sample_prompt:
                final_prompt = sample_prompt.replace('[trigger]', concept_sentence)
            else:
                final_prompt = sample_prompt

            # Generate 2 variations of the prompt for testing
            sample_config['prompts'] = [
                final_prompt,
                final_prompt + ", high quality, detailed"
            ]
        else:
            # Default prompts with trigger word
            default_prompts = [
                f"a photo of {concept_sentence}, high quality" if concept_sentence else "a high quality photo",
                f"{concept_sentence} in a professional setting" if concept_sentence else "professional photo"
            ]
            sample_config['prompts'] = default_prompts

        # Ensure sampling every 250 steps
        sample_config['sample_every'] = 250
        sample_config['walk_seed'] = True  # Use different seeds for variety

    # Set trigger word
    if concept_sentence:
        process_config['trigger_word'] = concept_sentence

    # Save modified config
    job_id = str(uuid.uuid4())
    config_path = os.path.join('tmp', f"{job_id}-{slugged_name}.yaml")
    os.makedirs('tmp', exist_ok=True)

    with open(config_path, 'w') as f:
        yaml.dump(config, f)

    return config_path, job_id

@app.route('/api/train', methods=['POST'])
@require_auth
def start_training():
    """Start LoRA training with selected configuration"""
    global training_status

    if training_status['is_training']:
        return jsonify({'error': 'Training already in progress'}), 400

    data = request.get_json()

    # Calculate training cost if billing is enabled
    if BILLING_ENABLED:
        user_tier = ServiceTier(request.user.get('tier', 'starter'))

        # Count images in session
        session_id = data.get('session_id')
        session_folder = os.path.join(UPLOAD_FOLDER, session_id)
        num_images = len([f for f in os.listdir(session_folder)
                         if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif'))])

        cost_info = pricing_engine.calculate_training_cost(
            steps=data.get('steps', 1000),
            batch_size=data.get('batch_size', 1),
            rank=data.get('rank', 16),
            num_images=num_images,
            model_type=data.get('config_name', 'flux'),
            user_tier=user_tier
        )

        required_tokens = cost_info['total_tokens']
        user_id = request.user['user_id']

        # Check and deduct tokens
        deduction_result = billing_manager.deduct_tokens(
            user_id,
            required_tokens,
            f"LoRA Training: {data.get('lora_name', 'Unnamed')}"
        )

        if not deduction_result['success']:
            return jsonify(deduction_result), 402

    # Extract required parameters
    session_id = data.get('session_id')
    config_name = data.get('config_name')
    lora_name = data.get('lora_name')
    concept_sentence = data.get('concept_sentence', '')
    sample_prompt = data.get('sample_prompt', '')
    captions = data.get('captions', {})

    # Validation
    if not all([session_id, config_name, lora_name]):
        return jsonify({'error': 'Missing required parameters: session_id, config_name, lora_name'}), 400

    session_folder = os.path.join(UPLOAD_FOLDER, session_id)
    if not os.path.exists(session_folder):
        return jsonify({'error': 'Session not found'}), 404

    try:
        # Create dataset
        dataset_path = create_dataset_folder(session_folder, captions, concept_sentence)

        # Get base config path
        base_config_path = os.path.join(CONFIG_FOLDER, f"{config_name}.yaml")
        if not os.path.exists(base_config_path):
            return jsonify({'error': 'Configuration file not found'}), 404

        # Prepare training configuration
        config_path, job_id = prepare_training_config(
            base_config_path,
            dataset_path,
            lora_name,
            concept_sentence,
            sample_prompt,
            steps=data.get('steps', 1000),
            learning_rate=data.get('learning_rate', 1e-4),
            rank=data.get('rank', 16),
            batch_size=data.get('batch_size', 1)
        )

        # Update training status
        training_status.update({
            'is_training': True,
            'progress': 0,
            'message': 'Starting training...',
            'job_id': job_id,
            'error': None
        })

        # Start training in background thread using the existing run.py command
        def run_training():
            try:
                # Use subprocess to run the existing training command
                cmd = [sys.executable, 'run.py', config_path]
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=os.getcwd()
                )

                # Wait for completion
                _, stderr = process.communicate()

                if process.returncode == 0:
                    training_status.update({
                        'is_training': False,
                        'progress': 100,
                        'message': f'Training completed successfully. Model saved as {slugify(lora_name)}',
                        'error': None
                    })
                else:
                    training_status.update({
                        'is_training': False,
                        'progress': 0,
                        'message': 'Training failed',
                        'error': stderr or 'Unknown error'
                    })

            except Exception as e:
                training_status.update({
                    'is_training': False,
                    'progress': 0,
                    'message': 'Training failed',
                    'error': str(e)
                })

        training_thread = threading.Thread(target=run_training)
        training_thread.daemon = True
        training_thread.start()

        return jsonify({
            'job_id': job_id,
            'message': 'Training started successfully',
            'dataset_path': dataset_path,
            'config_path': config_path
        })

    except Exception as e:
        return jsonify({'error': f'Failed to start training: {str(e)}'}), 500

@app.route('/api/training/status')
def get_training_status():
    """Get current training status"""
    return jsonify(training_status)

@app.route('/api/training/stop', methods=['POST'])
def stop_training():
    """Stop current training"""
    global training_status

    if not training_status['is_training']:
        return jsonify({'error': 'No training in progress'}), 400

    # Simple implementation - just update status
    # In a real scenario, you'd need to kill the subprocess
    training_status.update({
        'is_training': False,
        'progress': 0,
        'message': 'Training stopped by user',
        'error': None
    })

    return jsonify({'message': 'Training stop requested'})

@app.route('/uploads/<session_id>/<filename>')
def uploaded_file(session_id, filename):
    """Serve uploaded files"""
    return send_from_directory(os.path.join(UPLOAD_FOLDER, session_id), filename)

@app.route('/api/models')
def list_trained_models():
    """List all trained models available for download"""
    models = []
    output_dir = "output"  # Default output directory

    if os.path.exists(output_dir):
        for item in os.listdir(output_dir):
            item_path = os.path.join(output_dir, item)
            if os.path.isdir(item_path):
                # Check if it's a completed training folder
                model_info = {
                    'name': item,
                    'path': item_path,
                    'created': os.path.getctime(item_path),
                    'size': get_folder_size(item_path),
                    'files': []
                }

                # List important files
                for file in os.listdir(item_path):
                    if file.endswith(('.safetensors', '.ckpt', '.pt', '.yaml', '.json')):
                        file_path = os.path.join(item_path, file)
                        model_info['files'].append({
                            'name': file,
                            'size': os.path.getsize(file_path),
                            'modified': os.path.getmtime(file_path)
                        })

                models.append(model_info)

    return jsonify({'models': models})

def get_folder_size(folder_path):
    """Calculate total size of a folder"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(folder_path):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            if os.path.exists(file_path):
                total_size += os.path.getsize(file_path)
    return total_size

@app.route('/api/models/<model_name>/download')
def download_model(model_name):
    """Download a trained model as a zip file"""
    import zipfile
    import tempfile

    model_path = os.path.join("output", model_name)
    if not os.path.exists(model_path):
        return jsonify({'error': 'Model not found'}), 404

    # Create a temporary zip file
    temp_dir = tempfile.mkdtemp()
    zip_path = os.path.join(temp_dir, f"{model_name}.zip")

    try:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(model_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, model_path)
                    zipf.write(file_path, arcname)

        return send_file(
            zip_path,
            as_attachment=True,
            download_name=f"{model_name}.zip",
            mimetype='application/zip'
        )

    except Exception as e:
        return jsonify({'error': f'Failed to create zip: {str(e)}'}), 500

@app.route('/api/models/<model_name>/samples')
def get_model_samples(model_name):
    """Get sample images generated during training"""
    model_path = os.path.join("output", model_name)
    samples_path = os.path.join(model_path, "samples")

    if not os.path.exists(samples_path):
        return jsonify({'samples': []})

    samples = []
    for file in os.listdir(samples_path):
        if file.lower().endswith(('.png', '.jpg', '.jpeg')):
            file_path = os.path.join(samples_path, file)
            samples.append({
                'filename': file,
                'path': f"/api/models/{model_name}/samples/{file}",
                'size': os.path.getsize(file_path),
                'created': os.path.getctime(file_path)
            })

    # Sort by creation time (newest first)
    samples.sort(key=lambda x: x['created'], reverse=True)
    return jsonify({'samples': samples})

@app.route('/api/models/<model_name>/samples/<filename>')
def serve_sample_image(model_name, filename):
    """Serve sample images from training"""
    samples_path = os.path.join("output", model_name, "samples")
    return send_from_directory(samples_path, filename)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5006)
