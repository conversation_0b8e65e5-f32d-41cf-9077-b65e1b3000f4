import os
import sys
import uuid
import json
import yaml
import shutil
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename
from PIL import Image
import torch
from transformers import AutoProcessor, AutoModelForCausalLM
from slugify import slugify

# Add the current working directory to the Python path
sys.path.insert(0, os.getcwd())

# Import AI toolkit modules
from toolkit.job import get_job
from toolkit.config import get_config

# Import our utilities
from utils.config_parser import ConfigParser
from utils.training_manager import TrainingManager

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
DATASET_FOLDER = 'datasets'
OUTPUT_FOLDER = 'output'
CONFIG_FOLDER = 'config'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'txt'}
MAX_CONTENT_LENGTH = 500 * 1024 * 1024  # 500MB max file size

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Create necessary directories
for folder in [UPLOAD_FOLDER, DATASET_FOLDER, OUTPUT_FOLDER]:
    os.makedirs(folder, exist_ok=True)

# Initialize utilities
config_parser = ConfigParser(CONFIG_FOLDER)
training_manager = TrainingManager(DATASET_FOLDER, OUTPUT_FOLDER)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS



@app.route('/')
def index():
    """Main page with upload and configuration interface"""
    configs = config_parser.get_all_configs()
    return render_template('index.html', configs=configs)

@app.route('/api/configs')
def api_configs():
    """API endpoint to get available configurations"""
    configs = config_parser.get_all_configs()
    return jsonify(configs)

@app.route('/api/config/<config_name>')
def api_config_details(config_name):
    """API endpoint to get detailed configuration"""
    config = config_parser.get_config(config_name)
    if not config:
        return jsonify({'error': 'Config not found'}), 404

    return jsonify(config)

@app.route('/upload', methods=['POST'])
def upload_files():
    """Handle file uploads"""
    if 'files' not in request.files:
        return jsonify({'error': 'No files provided'}), 400
    
    files = request.files.getlist('files')
    if not files or files[0].filename == '':
        return jsonify({'error': 'No files selected'}), 400
    
    # Create unique upload session
    session_id = str(uuid.uuid4())
    session_folder = os.path.join(UPLOAD_FOLDER, session_id)
    os.makedirs(session_folder, exist_ok=True)
    
    uploaded_files = []
    
    for file in files:
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            file_path = os.path.join(session_folder, filename)
            file.save(file_path)
            
            # Get file info
            file_info = {
                'filename': filename,
                'path': file_path,
                'size': os.path.getsize(file_path),
                'type': 'image' if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')) else 'video' if filename.lower().endswith(('.mp4', '.avi', '.mov')) else 'text'
            }
            
            # If it's an image, get dimensions
            if file_info['type'] == 'image':
                try:
                    with Image.open(file_path) as img:
                        file_info['width'], file_info['height'] = img.size
                except Exception as e:
                    print(f"Error getting image dimensions: {e}")
            
            uploaded_files.append(file_info)
    
    return jsonify({
        'session_id': session_id,
        'files': uploaded_files,
        'message': f'Successfully uploaded {len(uploaded_files)} files'
    })

@app.route('/api/caption', methods=['POST'])
def auto_caption():
    """Generate captions for uploaded images"""
    data = request.get_json()
    session_id = data.get('session_id')
    concept_sentence = data.get('concept_sentence', '')
    
    if not session_id:
        return jsonify({'error': 'Session ID required'}), 400
    
    session_folder = os.path.join(UPLOAD_FOLDER, session_id)
    if not os.path.exists(session_folder):
        return jsonify({'error': 'Session not found'}), 404
    
    try:
        # Load Florence-2 model for captioning
        device = "cuda" if torch.cuda.is_available() else "cpu"
        torch_dtype = torch.float16
        
        model = AutoModelForCausalLM.from_pretrained(
            "multimodalart/Florence-2-large-no-flash-attn", 
            torch_dtype=torch_dtype, 
            trust_remote_code=True
        ).to(device)
        
        processor = AutoProcessor.from_pretrained(
            "multimodalart/Florence-2-large-no-flash-attn", 
            trust_remote_code=True
        )
        
        captions = {}
        
        # Process each image file
        for filename in os.listdir(session_folder):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                image_path = os.path.join(session_folder, filename)
                
                try:
                    image = Image.open(image_path).convert("RGB")
                    
                    prompt = "<DETAILED_CAPTION>"
                    inputs = processor(text=prompt, images=image, return_tensors="pt").to(device, torch_dtype)
                    
                    generated_ids = model.generate(
                        input_ids=inputs["input_ids"], 
                        pixel_values=inputs["pixel_values"], 
                        max_new_tokens=1024, 
                        num_beams=3
                    )
                    
                    generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]
                    parsed_answer = processor.post_process_generation(
                        generated_text, task=prompt, image_size=(image.width, image.height)
                    )
                    
                    caption_text = parsed_answer["<DETAILED_CAPTION>"].replace("The image shows ", "")
                    
                    if concept_sentence:
                        caption_text = f"{caption_text} [trigger]"
                    
                    captions[filename] = caption_text
                    
                except Exception as e:
                    print(f"Error captioning {filename}: {e}")
                    captions[filename] = f"Error generating caption: {str(e)}"
        
        # Clean up model
        model.to("cpu")
        del model
        del processor
        torch.cuda.empty_cache()
        
        return jsonify({
            'captions': captions,
            'message': f'Generated captions for {len(captions)} images'
        })
        
    except Exception as e:
        return jsonify({'error': f'Captioning failed: {str(e)}'}), 500

@app.route('/api/train', methods=['POST'])
def start_training():
    """Start LoRA training with selected configuration"""
    if training_manager.get_status()['is_training']:
        return jsonify({'error': 'Training already in progress'}), 400

    data = request.get_json()

    # Extract training parameters
    session_id = data.get('session_id')
    config_name = data.get('config_name')
    lora_name = data.get('lora_name')
    concept_sentence = data.get('concept_sentence', '')
    captions = data.get('captions', {})

    # Training parameters
    training_params = {
        'lora_name': lora_name,
        'config_name': config_name,
        'concept_sentence': concept_sentence,
        'steps': data.get('steps', 1000),
        'learning_rate': data.get('learning_rate', 1e-4),
        'rank': data.get('rank', 16),
        'batch_size': data.get('batch_size', 1),
        'save_every': data.get('save_every', 250)
    }

    # Validation
    errors = training_manager.validate_training_params(training_params)
    if errors:
        return jsonify({'error': 'Validation errors: ' + ', '.join(errors)}), 400

    if not session_id:
        return jsonify({'error': 'Session ID required'}), 400

    session_folder = os.path.join(UPLOAD_FOLDER, session_id)
    if not os.path.exists(session_folder):
        return jsonify({'error': 'Session not found'}), 404

    try:
        # Create dataset
        dataset_path = training_manager.create_dataset(session_folder, captions, concept_sentence)
        training_params['dataset_path'] = dataset_path

        # Get base config path
        base_config_path = os.path.join(CONFIG_FOLDER, f"{config_name}.yaml")
        if not os.path.exists(base_config_path):
            return jsonify({'error': 'Configuration file not found'}), 404

        # Prepare training configuration
        config_path, job_id = training_manager.prepare_config(base_config_path, training_params)

        # Start training
        training_manager.start_training(config_path, job_id)

        return jsonify({
            'job_id': job_id,
            'message': 'Training started successfully',
            'dataset_path': dataset_path,
            'config_path': config_path
        })

    except Exception as e:
        return jsonify({'error': f'Failed to start training: {str(e)}'}), 500

@app.route('/api/training/status')
def get_training_status():
    """Get current training status"""
    return jsonify(training_manager.get_status())

@app.route('/api/training/stop', methods=['POST'])
def stop_training():
    """Stop current training"""
    if not training_manager.get_status()['is_training']:
        return jsonify({'error': 'No training in progress'}), 400

    success = training_manager.stop_training()
    if success:
        return jsonify({'message': 'Training stop requested'})
    else:
        return jsonify({'error': 'Failed to stop training'}), 500

@app.route('/uploads/<session_id>/<filename>')
def uploaded_file(session_id, filename):
    """Serve uploaded files"""
    return send_from_directory(os.path.join(UPLOAD_FOLDER, session_id), filename)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
