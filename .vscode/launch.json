{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Run current config",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/run.py",
            "args": [
                "${file}"
            ],
            "env": {
                "CUDA_LAUNCH_BLOCKING": "1",
                "DEBUG_TOOLKIT": "1"
            },
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "Run current config (cuda:1)",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/run.py",
            "args": [
                "${file}"
            ],
            "env": {
                "CUDA_LAUNCH_BLOCKING": "1",
                "DEBUG_TOOLKIT": "1",
                "CUDA_VISIBLE_DEVICES": "1"
            },
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "Python: Debug Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": false
        },
    ]
}