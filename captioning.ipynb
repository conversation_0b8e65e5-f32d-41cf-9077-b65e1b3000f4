{"cells": [{"cell_type": "code", "execution_count": null, "id": "3b356cce", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["import torch\n", "from PIL import Image\n", "from transformers import Blip2Processor, Blip2ForConditionalGeneration\n", "from pathlib import Path\n", "import os\n", "from tqdm import tqdm\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"CUDA device: {torch.cuda.get_device_name()}\")"]}, {"cell_type": "code", "execution_count": null, "id": "768ad63a", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["class SimpleCaptionGenerator:\n", "    def __init__(self, model_name=\"Salesforce/blip2-opt-2.7b\"):\n", "        \"\"\"\n", "        Initialize the caption generator with BLIP2\n", "        \"\"\"\n", "        self.device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "        self.model_name = model_name\n", "        self.processor = None\n", "        self.model = None\n", "        \n", "        print(f\"Using device: {self.device}\")\n", "        \n", "    def load_model(self):\n", "        \"\"\"\n", "        Load BLIP2 model and processor\n", "        \"\"\"\n", "        print(f\"Loading BLIP2 model: {self.model_name}\")\n", "        \n", "        # Load processor\n", "        self.processor = Blip2Processor.from_pretrained(self.model_name)\n", "        \n", "        # Load model\n", "        self.model = Blip2ForConditionalGeneration.from_pretrained(\n", "            self.model_name,\n", "            torch_dtype=torch.float16 if self.device == \"cuda\" else torch.float32,\n", "            device_map=\"auto\" if self.device == \"cuda\" else None\n", "        )\n", "        \n", "        if self.device == \"cuda\":\n", "            self.model = self.model.to(self.device)\n", "            \n", "        print(\"✓ Model loaded successfully!\")\n", "        \n", "    def generate_caption(self, image_path, prompt=None, max_length=100):\n", "        \"\"\"\n", "        Generate caption for a single image\n", "        \"\"\"\n", "        # Load image\n", "        image = Image.open(image_path)\n", "        if image.mode != 'RGB':\n", "            image = image.convert('RGB')\n", "            \n", "        # Prepare inputs\n", "        if prompt:\n", "            inputs = self.processor(image, text=prompt, return_tensors=\"pt\")\n", "        else:\n", "            inputs = self.processor(image, return_tensors=\"pt\")\n", "            \n", "        if self.device == \"cuda\":\n", "            inputs = {k: v.to(self.device) for k, v in inputs.items()}\n", "            \n", "        # Generate caption\n", "        with torch.no_grad():\n", "            generated_ids = self.model.generate(\n", "                **inputs,\n", "                max_length=max_length,\n", "                num_beams=5,\n", "                temperature=0.7,\n", "                do_sample=True,\n", "                early_stopping=True\n", "            )\n", "            \n", "        # Decode caption\n", "        caption = self.processor.decode(generated_ids[0], skip_special_tokens=True)\n", "        \n", "        # Clean caption\n", "        caption = caption.strip()\n", "        if caption and not caption[0].isupper():\n", "            caption = caption[0].upper() + caption[1:]\n", "        if caption and caption[-1] not in '.!?':\n", "            caption += '.'\n", "            \n", "        return caption\n", "    \n", "    def save_caption(self, image_path, caption, output_dir=None):\n", "        \"\"\"\n", "        Save caption to txt file with same name as image\n", "        \"\"\"\n", "        image_path = Path(image_path)\n", "        \n", "        if output_dir:\n", "            output_path = Path(output_dir)\n", "            output_path.mkdir(exist_ok=True)\n", "            caption_file = output_path / f\"{image_path.stem}.txt\"\n", "        else:\n", "            caption_file = image_path.parent / f\"{image_path.stem}.txt\"\n", "            \n", "        with open(caption_file, 'w', encoding='utf-8') as f:\n", "            f.write(caption)\n", "            \n", "        return caption_file\n", "    \n", "    def process_single_image(self, image_path, output_dir=None, prompt=None):\n", "        \"\"\"\n", "        Process a single image: generate caption and save to txt file\n", "        \"\"\"\n", "        try:\n", "            caption = self.generate_caption(image_path, prompt)\n", "            caption_file = self.save_caption(image_path, caption, output_dir)\n", "            \n", "            return {\n", "                'image_path': str(image_path),\n", "                'caption': caption,\n", "                'caption_file': str(caption_file),\n", "                'success': True\n", "            }\n", "        except Exception as e:\n", "            return {\n", "                'image_path': str(image_path),\n", "                'error': str(e),\n", "                'success': <PERSON><PERSON><PERSON>\n", "            }\n", "    \n", "    def process_directory(self, input_dir, output_dir=None, prompt=None):\n", "        \"\"\"\n", "        Process all images in a directory\n", "        \"\"\"\n", "        input_path = Path(input_dir)\n", "        \n", "        # Find all image files\n", "        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}\n", "        image_files = []\n", "        \n", "        for ext in image_extensions:\n", "            image_files.extend(input_path.glob(f\"*{ext}\"))\n", "            image_files.extend(input_path.glob(f\"*{ext.upper()}\"))\n", "            \n", "        if not image_files:\n", "            print(f\"No image files found in {input_dir}\")\n", "            return []\n", "            \n", "        print(f\"Found {len(image_files)} images to process\")\n", "        \n", "        results = []\n", "        successful = 0\n", "        failed = 0\n", "        \n", "        for image_file in tqdm(image_files, desc=\"Processing images\"):\n", "            result = self.process_single_image(image_file, output_dir, prompt)\n", "            results.append(result)\n", "            \n", "            if result['success']:\n", "                successful += 1\n", "            else:\n", "                failed += 1\n", "                print(f\"Failed to process {image_file}: {result['error']}\")\n", "                \n", "        print(f\"\\nProcessing complete:\")\n", "        print(f\"✓ Successful: {successful}\")\n", "        print(f\"✗ Failed: {failed}\")\n", "        \n", "        return results"]}, {"cell_type": "code", "execution_count": null, "id": "8681c15d", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# Initialize the caption generator\n", "generator = SimpleCaptionGenerator()\n", "\n", "# Load the model (this may take a few minutes on first run)\n", "generator.load_model()"]}, {"cell_type": "code", "execution_count": null, "id": "228eb30a", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# Example: Process all images in a directory\n", "# Replace with your actual directory paths\n", "\n", "input_directory = \"path/to/your/images\"      # Directory containing images\n", "output_directory = \"path/to/your/captions\"   # Directory to save caption files\n", "\n", "# Check if input directory exists\n", "if os.path.exists(input_directory):\n", "    results = generator.process_directory(input_directory, output_directory)\n", "    \n", "    # Show some example results\n", "    print(\"\\nExample results:\")\n", "    for i, result in enumerate(results[:3]):  # Show first 3 results\n", "        if result['success']:\n", "            print(f\"\\n{i+1}. {Path(result['image_path']).name}\")\n", "            print(f\"   Caption: {result['caption']}\")\n", "else:\n", "    print(f\"Directory not found: {input_directory}\")\n", "    print(\"Please update the input_directory variable with a valid directory path.\")"]}, {"cell_type": "code", "execution_count": null, "id": "a289bc45", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# Example: Generate more detailed captions using custom prompts\n", "\n", "image_path = \"path/to/your/image.jpg\"  # Change this to your image path\n", "\n", "if os.path.exists(image_path):\n", "    # Different prompts for different levels of detail\n", "    prompts = {\n", "        \"basic\": None,  # No prompt for basic caption\n", "        \"detailed\": \"Describe this image in detail, including objects, setting, and activities:\",\n", "        \"very_detailed\": \"Provide a comprehensive description of this image including all visible objects, people, setting, lighting, colors, mood, and any activities taking place:\"\n", "    }\n", "    \n", "    for level, prompt in prompts.items():\n", "        print(f\"\\n{level.upper()} CAPTION:\")\n", "        print(\"-\" * 40)\n", "        \n", "        caption = generator.generate_caption(image_path, prompt, max_length=150)\n", "        print(caption)\n", "        \n", "        # Save with level suffix\n", "        image_file = Path(image_path)\n", "        caption_file = image_file.parent / f\"{image_file.stem}_{level}.txt\"\n", "        \n", "        with open(caption_file, 'w', encoding='utf-8') as f:\n", "            f.write(caption)\n", "        \n", "        print(f\"Saved to: {caption_file}\")\n", "else:\n", "    print(f\"Image not found: {image_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "77fe4504", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# Clean up GPU memory when done\n", "import gc\n", "\n", "del generator\n", "gc.collect()\n", "\n", "if torch.cuda.is_available():\n", "    torch.cuda.empty_cache()\n", "    \n", "print(\"Memory cleaned up!\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}