import torch
from PIL import Image
from transformers import <PERSON><PERSON>2Processor, Blip2ForConditionalGeneration
from pathlib import Path
import os
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA device: {torch.cuda.get_device_name()}")

class SimpleCaptionGenerator:
    def __init__(self, model_name="Salesforce/blip2-opt-2.7b"):
        """
        Initialize the caption generator with BLIP2
        """
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model_name = model_name
        self.processor = None
        self.model = None
        
        print(f"Using device: {self.device}")
        
    def load_model(self):
        """
        Load BLIP2 model and processor
        """
        print(f"Loading BLIP2 model: {self.model_name}")
        
        # Load processor
        self.processor = Blip2Processor.from_pretrained(self.model_name)
        
        # Load model
        self.model = Blip2ForConditionalGeneration.from_pretrained(
            self.model_name,
            torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
            device_map="auto" if self.device == "cuda" else None
        )
        
        if self.device == "cuda":
            self.model = self.model.to(self.device)
            
        print("✓ Model loaded successfully!")
        
    def generate_caption(self, image_path, prompt=None, max_length=100):
        """
        Generate caption for a single image
        """
        # Load image
        image = Image.open(image_path)
        if image.mode != 'RGB':
            image = image.convert('RGB')
            
        # Prepare inputs
        if prompt:
            inputs = self.processor(image, text=prompt, return_tensors="pt")
        else:
            inputs = self.processor(image, return_tensors="pt")
            
        if self.device == "cuda":
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
        # Generate caption
        with torch.no_grad():
            generated_ids = self.model.generate(
                **inputs,
                max_length=max_length,
                num_beams=5,
                temperature=0.7,
                do_sample=True,
                early_stopping=True
            )
            
        # Decode caption
        caption = self.processor.decode(generated_ids[0], skip_special_tokens=True)
        
        # Clean caption
        caption = caption.strip()
        if caption and not caption[0].isupper():
            caption = caption[0].upper() + caption[1:]
        if caption and caption[-1] not in '.!?':
            caption += '.'
            
        return caption
    
    def save_caption(self, image_path, caption, output_dir=None):
        """
        Save caption to txt file with same name as image
        """
        image_path = Path(image_path)
        
        if output_dir:
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            caption_file = output_path / f"{image_path.stem}.txt"
        else:
            caption_file = image_path.parent / f"{image_path.stem}.txt"
            
        with open(caption_file, 'w', encoding='utf-8') as f:
            f.write(caption)
            
        return caption_file
    
    def process_single_image(self, image_path, output_dir=None, prompt=None):
        """
        Process a single image: generate caption and save to txt file
        """
        try:
            caption = self.generate_caption(image_path, prompt)
            caption_file = self.save_caption(image_path, caption, output_dir)
            
            return {
                'image_path': str(image_path),
                'caption': caption,
                'caption_file': str(caption_file),
                'success': True
            }
        except Exception as e:
            return {
                'image_path': str(image_path),
                'error': str(e),
                'success': False
            }
    
    def process_directory(self, input_dir, output_dir=None, prompt=None):
        """
        Process all images in a directory
        """
        input_path = Path(input_dir)
        
        # Find all image files
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
            
        if not image_files:
            print(f"No image files found in {input_dir}")
            return []
            
        print(f"Found {len(image_files)} images to process")
        
        results = []
        successful = 0
        failed = 0
        
        for image_file in tqdm(image_files, desc="Processing images"):
            result = self.process_single_image(image_file, output_dir, prompt)
            results.append(result)
            
            if result['success']:
                successful += 1
            else:
                failed += 1
                print(f"Failed to process {image_file}: {result['error']}")
                
        print(f"\nProcessing complete:")
        print(f"✓ Successful: {successful}")
        print(f"✗ Failed: {failed}")
        
        return results

# Initialize the caption generator
generator = SimpleCaptionGenerator()

# Load the model (this may take a few minutes on first run)
generator.load_model()

# Example: Process all images in a directory
# Replace with your actual directory paths

input_directory = "path/to/your/images"      # Directory containing images
output_directory = "path/to/your/captions"   # Directory to save caption files

# Check if input directory exists
if os.path.exists(input_directory):
    results = generator.process_directory(input_directory, output_directory)
    
    # Show some example results
    print("\nExample results:")
    for i, result in enumerate(results[:3]):  # Show first 3 results
        if result['success']:
            print(f"\n{i+1}. {Path(result['image_path']).name}")
            print(f"   Caption: {result['caption']}")
else:
    print(f"Directory not found: {input_directory}")
    print("Please update the input_directory variable with a valid directory path.")

# Example: Generate more detailed captions using custom prompts

image_path = "path/to/your/image.jpg"  # Change this to your image path

if os.path.exists(image_path):
    # Different prompts for different levels of detail
    prompts = {
        "basic": None,  # No prompt for basic caption
        "detailed": "Describe this image in detail, including objects, setting, and activities:",
        "very_detailed": "Provide a comprehensive description of this image including all visible objects, people, setting, lighting, colors, mood, and any activities taking place:"
    }
    
    for level, prompt in prompts.items():
        print(f"\n{level.upper()} CAPTION:")
        print("-" * 40)
        
        caption = generator.generate_caption(image_path, prompt, max_length=150)
        print(caption)
        
        # Save with level suffix
        image_file = Path(image_path)
        caption_file = image_file.parent / f"{image_file.stem}_{level}.txt"
        
        with open(caption_file, 'w', encoding='utf-8') as f:
            f.write(caption)
        
        print(f"Saved to: {caption_file}")
else:
    print(f"Image not found: {image_path}")

# Clean up GPU memory when done
import gc

del generator
gc.collect()

if torch.cuda.is_available():
    torch.cuda.empty_cache()
    
print("Memory cleaned up!")