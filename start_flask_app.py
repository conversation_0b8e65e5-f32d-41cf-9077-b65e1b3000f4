#!/usr/bin/env python3
"""
Startup script for the AI Toolkit Flask Application
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_requirements():
    """Check if required packages are installed"""
    required_packages = [
        'flask',
        'flask-cors',
        'torch',
        'transformers',
        'pillow',
        'pyyaml',
        'python-slugify'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_ai_toolkit():
    """Check if AI Toolkit is properly set up"""
    try:
        sys.path.insert(0, os.getcwd())
        from toolkit.job import get_job
        print("✅ AI Toolkit found and accessible")
        return True
    except ImportError as e:
        print(f"❌ AI Toolkit not found: {e}")
        print("💡 Make sure you're running this from the AI Toolkit root directory")
        return False

def check_config_files():
    """Check if configuration files exist"""
    config_dir = Path('config')
    if not config_dir.exists():
        print("❌ Config directory not found")
        return False
    
    yaml_files = list(config_dir.glob('*.yaml'))
    if not yaml_files:
        print("❌ No YAML configuration files found in config/")
        print("💡 Add some configuration files to get started")
        return False
    
    print(f"✅ Found {len(yaml_files)} configuration file(s)")
    for config_file in yaml_files[:5]:  # Show first 5
        print(f"   - {config_file.name}")
    if len(yaml_files) > 5:
        print(f"   ... and {len(yaml_files) - 5} more")
    
    return True

def create_directories():
    """Create necessary directories"""
    directories = ['uploads', 'datasets', 'output', 'tmp', 'templates', 'utils']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ Created necessary directories")

def main():
    parser = argparse.ArgumentParser(description='Start the AI Toolkit Flask Application')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000, help='Port to bind to (default: 5000)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--skip-checks', action='store_true', help='Skip pre-flight checks')
    
    args = parser.parse_args()
    
    print("🚀 Starting AI Toolkit Flask Application")
    print("=" * 50)
    
    if not args.skip_checks:
        print("🔍 Running pre-flight checks...")
        
        # Check requirements
        if not check_requirements():
            sys.exit(1)
        
        # Check AI Toolkit
        if not check_ai_toolkit():
            sys.exit(1)
        
        # Check config files
        if not check_config_files():
            print("⚠️  Warning: No config files found, but continuing anyway")
        
        # Create directories
        create_directories()
        
        print("✅ All checks passed!")
        print()
    
    # Check if app.py exists
    if not Path('app.py').exists():
        print("❌ app.py not found in current directory")
        sys.exit(1)
    
    print(f"🌐 Starting Flask app on http://{args.host}:{args.port}")
    print("📱 The interface will be accessible in your web browser")
    print("🛑 Press Ctrl+C to stop the server")
    print()
    
    # Set environment variables
    env = os.environ.copy()
    if args.debug:
        env['FLASK_DEBUG'] = '1'
    
    # Start the Flask app
    try:
        cmd = [
            sys.executable, 'app.py'
        ]
        
        # If we want to override host/port, we'd need to modify app.py
        # For now, the app.py has these hardcoded
        
        subprocess.run(cmd, env=env)
        
    except KeyboardInterrupt:
        print("\n👋 Shutting down Flask application")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
