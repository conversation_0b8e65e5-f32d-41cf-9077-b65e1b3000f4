"""
Professional Token-Based Pricing Engine for LoRA Training Studio
Advanced mathematical formulas for cost calculation and billing management
"""

import math
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class ServiceTier(Enum):
    """Service tiers with different pricing multipliers"""
    STARTER = "starter"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"

class ResourceType(Enum):
    """Types of computational resources"""
    GPU_H100 = "gpu_h100"
    GPU_A100 = "gpu_a100"
    GPU_V100 = "gpu_v100"
    CPU_COMPUTE = "cpu_compute"
    STORAGE = "storage"
    BANDWIDTH = "bandwidth"

@dataclass
class PricingConfig:
    """Configuration for pricing calculations"""
    # Base rates per hour (in tokens)
    base_rates = {
        ResourceType.GPU_H100: 1000,    # Premium GPU
        ResourceType.GPU_A100: 800,     # High-end GPU
        ResourceType.GPU_V100: 600,     # Standard GPU
        ResourceType.CPU_COMPUTE: 50,   # CPU processing
        ResourceType.STORAGE: 1,        # Per GB per hour
        ResourceType.BANDWIDTH: 10      # Per GB transfer
    }
    
    # Service tier multipliers
    tier_multipliers = {
        ServiceTier.STARTER: 1.0,
        ServiceTier.PROFESSIONAL: 0.85,     # 15% discount
        ServiceTier.ENTERPRISE: 0.70        # 30% discount
    }
    
    # Volume discounts (based on monthly usage)
    volume_discounts = {
        10000: 0.95,    # 5% off for 10K+ tokens
        50000: 0.90,    # 10% off for 50K+ tokens
        100000: 0.85,   # 15% off for 100K+ tokens
        500000: 0.80,   # 20% off for 500K+ tokens
        1000000: 0.75   # 25% off for 1M+ tokens
    }

class TokenPricingEngine:
    """Advanced token pricing engine with mathematical cost optimization"""
    
    def __init__(self, db_path: str = "billing.db"):
        self.config = PricingConfig()
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize billing database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                user_id TEXT PRIMARY KEY,
                email TEXT UNIQUE,
                tier TEXT DEFAULT 'starter',
                token_balance INTEGER DEFAULT 0,
                total_spent INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Transactions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT,
                transaction_type TEXT,
                amount INTEGER,
                description TEXT,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        ''')
        
        # Usage logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS usage_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT,
                service_type TEXT,
                resource_type TEXT,
                duration_minutes REAL,
                tokens_consumed INTEGER,
                job_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def calculate_training_cost(self, 
                              steps: int,
                              batch_size: int,
                              rank: int,
                              num_images: int,
                              model_type: str,
                              user_tier: ServiceTier = ServiceTier.STARTER) -> Dict:
        """
        Advanced mathematical formula for LoRA training cost calculation
        
        Formula Components:
        1. Base Computational Cost = f(steps, batch_size, rank, model_complexity)
        2. Data Processing Cost = g(num_images, image_resolution, preprocessing)
        3. Storage Cost = h(model_size, checkpoint_frequency, sample_generation)
        4. Infrastructure Overhead = i(gpu_type, memory_usage, network_io)
        
        Total Cost = (Base + Data + Storage + Infrastructure) × Tier_Multiplier × Volume_Discount
        """
        
        # Model complexity factors
        model_complexity = {
            'flux': 2.5,        # Most complex
            'hidream': 2.0,     # High complexity
            'wan21_14b': 1.8,   # Large model
            'wan21_1b': 1.2,    # Medium model
            'schnell': 1.0      # Base complexity
        }
        
        complexity_factor = model_complexity.get(model_type.lower(), 1.5)
        
        # 1. Base Computational Cost Calculation
        # Formula: C_base = steps × batch_size × rank² × complexity × time_factor
        time_per_step = 0.5 + (batch_size * 0.1) + (rank * 0.01)  # seconds
        total_training_time = (steps * time_per_step) / 3600  # hours
        
        base_compute_cost = (
            steps * 
            batch_size * 
            (rank ** 1.5) * 
            complexity_factor * 
            total_training_time
        ) / 1000  # Normalize to reasonable token range
        
        # 2. Data Processing Cost
        # Formula: C_data = num_images × resolution_factor × preprocessing_factor
        avg_resolution_factor = 1.5  # Assuming 1024x1024 average
        preprocessing_factor = 2.0   # BLIP-2 captioning + augmentation
        
        data_processing_cost = (
            num_images * 
            avg_resolution_factor * 
            preprocessing_factor * 
            0.5  # Base cost per image
        )
        
        # 3. Storage Cost
        # Formula: C_storage = model_size × checkpoint_freq × sample_generation
        estimated_model_size_gb = (rank * complexity_factor) / 100  # GB
        checkpoint_frequency = max(1, steps // 250)  # Every 250 steps
        sample_generation_cost = checkpoint_frequency * 2 * 0.1  # 2 samples per checkpoint
        
        storage_cost = (
            estimated_model_size_gb * 
            checkpoint_frequency * 
            self.config.base_rates[ResourceType.STORAGE] * 
            total_training_time
        ) + sample_generation_cost
        
        # 4. Infrastructure Overhead
        # Formula: C_infra = gpu_cost × memory_factor × network_factor
        gpu_type = self._determine_gpu_type(complexity_factor, rank)
        gpu_hourly_rate = self.config.base_rates[gpu_type]
        
        memory_factor = 1 + (rank / 100)  # Higher rank = more memory
        network_factor = 1 + (num_images / 1000)  # More images = more I/O
        
        infrastructure_cost = (
            gpu_hourly_rate * 
            total_training_time * 
            memory_factor * 
            network_factor
        )
        
        # Total base cost
        total_base_cost = (
            base_compute_cost + 
            data_processing_cost + 
            storage_cost + 
            infrastructure_cost
        )
        
        # Apply tier multiplier
        tier_multiplier = self.config.tier_multipliers[user_tier]
        cost_after_tier = total_base_cost * tier_multiplier
        
        # Apply volume discount (would need user's monthly usage)
        volume_discount = self._calculate_volume_discount("user_id")  # Placeholder
        final_cost = cost_after_tier * volume_discount
        
        return {
            'total_tokens': int(final_cost),
            'breakdown': {
                'base_compute': int(base_compute_cost),
                'data_processing': int(data_processing_cost),
                'storage': int(storage_cost),
                'infrastructure': int(infrastructure_cost),
                'tier_discount': f"{(1-tier_multiplier)*100:.0f}%",
                'volume_discount': f"{(1-volume_discount)*100:.0f}%"
            },
            'estimated_time': f"{total_training_time:.1f} hours",
            'gpu_type': gpu_type.value,
            'cost_per_hour': int(final_cost / total_training_time) if total_training_time > 0 else 0
        }
    
    def calculate_inference_cost(self,
                               num_generations: int,
                               resolution: str = "1024x1024",
                               model_complexity: float = 1.0,
                               user_tier: ServiceTier = ServiceTier.STARTER) -> Dict:
        """
        Calculate cost for model inference/generation
        
        Formula: C_inference = generations × resolution_factor × complexity × gpu_time
        """
        
        # Resolution factors
        resolution_factors = {
            "512x512": 0.5,
            "768x768": 0.75,
            "1024x1024": 1.0,
            "1536x1536": 2.0,
            "2048x2048": 4.0
        }
        
        resolution_factor = resolution_factors.get(resolution, 1.0)
        
        # Base generation time (seconds per image)
        base_generation_time = 3.0  # seconds
        total_time_hours = (num_generations * base_generation_time * resolution_factor * model_complexity) / 3600
        
        # GPU cost for inference
        gpu_cost = self.config.base_rates[ResourceType.GPU_A100] * total_time_hours
        
        # Apply tier multiplier
        tier_multiplier = self.config.tier_multipliers[user_tier]
        final_cost = gpu_cost * tier_multiplier
        
        return {
            'total_tokens': int(final_cost),
            'cost_per_generation': int(final_cost / num_generations) if num_generations > 0 else 0,
            'estimated_time': f"{total_time_hours * 60:.1f} minutes",
            'resolution': resolution,
            'tier_discount': f"{(1-tier_multiplier)*100:.0f}%"
        }
    
    def _determine_gpu_type(self, complexity_factor: float, rank: int) -> ResourceType:
        """Determine optimal GPU type based on workload"""
        if complexity_factor > 2.0 or rank > 64:
            return ResourceType.GPU_H100
        elif complexity_factor > 1.5 or rank > 32:
            return ResourceType.GPU_A100
        else:
            return ResourceType.GPU_V100
    
    def _calculate_volume_discount(self, user_id: str) -> float:
        """Calculate volume discount based on monthly usage"""
        # Get user's monthly token usage
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        thirty_days_ago = datetime.now() - timedelta(days=30)
        cursor.execute('''
            SELECT SUM(tokens_consumed) FROM usage_logs 
            WHERE user_id = ? AND created_at > ?
        ''', (user_id, thirty_days_ago))
        
        monthly_usage = cursor.fetchone()[0] or 0
        conn.close()
        
        # Apply volume discount
        for threshold, discount in sorted(self.config.volume_discounts.items(), reverse=True):
            if monthly_usage >= threshold:
                return discount
        
        return 1.0  # No discount
    
    def get_pricing_tiers(self) -> Dict:
        """Get available pricing tiers and their benefits"""
        return {
            'starter': {
                'name': 'Starter',
                'price_per_1k_tokens': 10,  # $10 per 1000 tokens
                'discount': '0%',
                'features': [
                    'Basic LoRA training',
                    'Standard GPU access',
                    'Community support',
                    'Basic sample generation'
                ],
                'limits': {
                    'max_training_steps': 2000,
                    'max_images_per_dataset': 100,
                    'max_concurrent_jobs': 1
                }
            },
            'professional': {
                'name': 'Professional',
                'price_per_1k_tokens': 8.5,  # 15% discount
                'discount': '15%',
                'features': [
                    'Advanced LoRA training',
                    'Priority GPU access',
                    'Email support',
                    'Advanced sample generation',
                    'Custom model architectures',
                    'API access'
                ],
                'limits': {
                    'max_training_steps': 10000,
                    'max_images_per_dataset': 1000,
                    'max_concurrent_jobs': 3
                }
            },
            'enterprise': {
                'name': 'Enterprise',
                'price_per_1k_tokens': 7.0,  # 30% discount
                'discount': '30%',
                'features': [
                    'Unlimited LoRA training',
                    'Dedicated GPU clusters',
                    '24/7 priority support',
                    'Custom sample generation',
                    'White-label solutions',
                    'Advanced API access',
                    'Custom integrations',
                    'SLA guarantees'
                ],
                'limits': {
                    'max_training_steps': 'unlimited',
                    'max_images_per_dataset': 'unlimited',
                    'max_concurrent_jobs': 'unlimited'
                }
            }
        }

# Token packages for purchase
TOKEN_PACKAGES = {
    'starter_pack': {
        'tokens': 1000,
        'price': 10,
        'bonus': 0,
        'popular': False
    },
    'professional_pack': {
        'tokens': 5000,
        'price': 45,
        'bonus': 500,  # 10% bonus
        'popular': True
    },
    'enterprise_pack': {
        'tokens': 25000,
        'price': 200,
        'bonus': 5000,  # 20% bonus
        'popular': False
    },
    'mega_pack': {
        'tokens': 100000,
        'price': 750,
        'bonus': 25000,  # 25% bonus
        'popular': False
    }
}
