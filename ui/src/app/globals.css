@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

@layer components {
  /* control */
  .aitk-react-select-container .aitk-react-select__control {
    @apply flex w-full h-8 min-h-0 px-0 text-sm bg-gray-800 border border-gray-700 rounded-sm hover:border-gray-600 items-center;
  }

  /* selected label */
  .aitk-react-select-container .aitk-react-select__single-value {
    @apply flex-1 min-w-0 truncate text-sm text-neutral-200;
  }

  /* invisible input (keeps focus & typing, never wraps) */
  .aitk-react-select-container .aitk-react-select__input-container {
    @apply text-neutral-200;
  }

  /* focus */
  .aitk-react-select-container .aitk-react-select__control--is-focused {
    @apply ring-2 ring-gray-600 border-transparent hover:border-transparent shadow-none;
  }

  /* menu */
  .aitk-react-select-container .aitk-react-select__menu {
    @apply bg-gray-800 border border-gray-700;
  }

  /* options */
  .aitk-react-select-container .aitk-react-select__option {
    @apply text-sm text-neutral-200 bg-gray-800 hover:bg-gray-700;
  }

  /* indicator separator */
  .aitk-react-select-container .aitk-react-select__indicator-separator {
    @apply bg-gray-600;
  }

  /* indicators */
  .aitk-react-select-container .aitk-react-select__indicators,
  .aitk-react-select-container .aitk-react-select__indicator {
    @apply py-0 flex items-center;
  }

  /* placeholder */
  .aitk-react-select-container .aitk-react-select__placeholder {
    @apply text-sm text-neutral-200;
  }
}



