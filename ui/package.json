{"name": "ai-toolkit-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start --port 8675", "build_and_start": "npm install && npm run update_db && npm run build && npm run start", "lint": "next lint", "update_db": "npx prisma generate && npx prisma db push", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,css,scss}\""}, "dependencies": {"@headlessui/react": "^2.2.0", "@monaco-editor/react": "^4.7.0", "@prisma/client": "^6.3.1", "axios": "^1.7.9", "classnames": "^2.5.1", "lucide-react": "^0.475.0", "next": "15.1.7", "node-cache": "^5.1.2", "prisma": "^6.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-global-hooks": "^1.3.5", "react-icons": "^5.5.0", "react-select": "^5.10.1", "sqlite3": "^5.1.7", "yaml": "^2.7.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "prettier": "^3.5.1", "prettier-basic": "^1.0.0", "tailwindcss": "^3.4.1", "typescript": "^5"}, "prettier": "prettier-basic"}