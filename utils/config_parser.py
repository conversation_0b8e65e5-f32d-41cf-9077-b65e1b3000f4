import os
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any

class ConfigParser:
    """Utility class for parsing and managing AI Toolkit configuration files"""
    
    def __init__(self, config_folder: str = 'config'):
        self.config_folder = Path(config_folder)
    
    def get_all_configs(self) -> Dict[str, Dict]:
        """Get all available YAML configuration files"""
        configs = {}
        
        if not self.config_folder.exists():
            return configs
        
        for config_file in self.config_folder.glob('*.yaml'):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                configs[config_file.stem] = {
                    'file': str(config_file),
                    'name': config_file.stem,
                    'data': config_data,
                    'model_info': self.extract_model_info(config_data),
                    'training_params': self.extract_training_params(config_data),
                    'network_params': self.extract_network_params(config_data)
                }
            except Exception as e:
                print(f"Error loading config {config_file}: {e}")
        
        return configs
    
    def get_config(self, config_name: str) -> Optional[Dict]:
        """Get a specific configuration by name"""
        config_file = self.config_folder / f"{config_name}.yaml"
        
        if not config_file.exists():
            return None
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            return {
                'name': config_name,
                'file': str(config_file),
                'data': config_data,
                'model_info': self.extract_model_info(config_data),
                'training_params': self.extract_training_params(config_data),
                'network_params': self.extract_network_params(config_data)
            }
        except Exception as e:
            print(f"Error loading config {config_name}: {e}")
            return None
    
    def extract_model_info(self, config_data: Dict) -> Dict[str, Any]:
        """Extract model information from config data"""
        try:
            if 'config' in config_data and 'process' in config_data['config']:
                process = config_data['config']['process'][0]
                if 'model' in process:
                    model_info = process['model']
                    return {
                        'name_or_path': model_info.get('name_or_path', 'Unknown'),
                        'is_flux': model_info.get('is_flux', False),
                        'is_xl': model_info.get('is_xl', False),
                        'is_v2': model_info.get('is_v2', False),
                        'arch': model_info.get('arch', 'Unknown'),
                        'quantize': model_info.get('quantize', False),
                        'low_vram': model_info.get('low_vram', False),
                        'assistant_lora_path': model_info.get('assistant_lora_path'),
                        'vae_path': model_info.get('vae_path')
                    }
        except Exception as e:
            print(f"Error extracting model info: {e}")
        
        return {
            'name_or_path': 'Unknown',
            'is_flux': False,
            'is_xl': False,
            'is_v2': False,
            'arch': 'Unknown',
            'quantize': False,
            'low_vram': False,
            'assistant_lora_path': None,
            'vae_path': None
        }
    
    def extract_training_params(self, config_data: Dict) -> Dict[str, Any]:
        """Extract training parameters from config data"""
        try:
            if 'config' in config_data and 'process' in config_data['config']:
                process = config_data['config']['process'][0]
                if 'train' in process:
                    train_info = process['train']
                    return {
                        'steps': train_info.get('steps', 1000),
                        'lr': train_info.get('lr', 1e-4),
                        'batch_size': train_info.get('batch_size', 1),
                        'gradient_accumulation_steps': train_info.get('gradient_accumulation_steps', 1),
                        'optimizer': train_info.get('optimizer', 'adamw8bit'),
                        'noise_scheduler': train_info.get('noise_scheduler', 'flowmatch'),
                        'dtype': train_info.get('dtype', 'bf16'),
                        'gradient_checkpointing': train_info.get('gradient_checkpointing', True),
                        'train_unet': train_info.get('train_unet', True),
                        'train_text_encoder': train_info.get('train_text_encoder', False),
                        'ema_config': train_info.get('ema_config', {}),
                        'skip_first_sample': train_info.get('skip_first_sample', False),
                        'disable_sampling': train_info.get('disable_sampling', False)
                    }
        except Exception as e:
            print(f"Error extracting training params: {e}")
        
        return {
            'steps': 1000,
            'lr': 1e-4,
            'batch_size': 1,
            'gradient_accumulation_steps': 1,
            'optimizer': 'adamw8bit',
            'noise_scheduler': 'flowmatch',
            'dtype': 'bf16',
            'gradient_checkpointing': True,
            'train_unet': True,
            'train_text_encoder': False,
            'ema_config': {},
            'skip_first_sample': False,
            'disable_sampling': False
        }
    
    def extract_network_params(self, config_data: Dict) -> Dict[str, Any]:
        """Extract network parameters from config data"""
        try:
            if 'config' in config_data and 'process' in config_data['config']:
                process = config_data['config']['process'][0]
                if 'network' in process:
                    network_info = process['network']
                    return {
                        'type': network_info.get('type', 'lora'),
                        'linear': network_info.get('linear', 16),
                        'linear_alpha': network_info.get('linear_alpha', 16),
                        'conv': network_info.get('conv', None),
                        'conv_alpha': network_info.get('conv_alpha', None),
                        'lokr_full_rank': network_info.get('lokr_full_rank', True),
                        'lokr_factor': network_info.get('lokr_factor', -1)
                    }
        except Exception as e:
            print(f"Error extracting network params: {e}")
        
        return {
            'type': 'lora',
            'linear': 16,
            'linear_alpha': 16,
            'conv': None,
            'conv_alpha': None,
            'lokr_full_rank': True,
            'lokr_factor': -1
        }
    
    def extract_sample_params(self, config_data: Dict) -> Dict[str, Any]:
        """Extract sampling parameters from config data"""
        try:
            if 'config' in config_data and 'process' in config_data['config']:
                process = config_data['config']['process'][0]
                if 'sample' in process:
                    sample_info = process['sample']
                    return {
                        'sampler': sample_info.get('sampler', 'flowmatch'),
                        'sample_every': sample_info.get('sample_every', 250),
                        'width': sample_info.get('width', 1024),
                        'height': sample_info.get('height', 1024),
                        'num_frames': sample_info.get('num_frames', None),
                        'fps': sample_info.get('fps', None),
                        'prompts': sample_info.get('prompts', []),
                        'neg': sample_info.get('neg', ''),
                        'seed': sample_info.get('seed', 42),
                        'walk_seed': sample_info.get('walk_seed', True),
                        'guidance_scale': sample_info.get('guidance_scale', 4),
                        'sample_steps': sample_info.get('sample_steps', 20)
                    }
        except Exception as e:
            print(f"Error extracting sample params: {e}")
        
        return {
            'sampler': 'flowmatch',
            'sample_every': 250,
            'width': 1024,
            'height': 1024,
            'num_frames': None,
            'fps': None,
            'prompts': [],
            'neg': '',
            'seed': 42,
            'walk_seed': True,
            'guidance_scale': 4,
            'sample_steps': 20
        }
    
    def get_supported_models(self) -> List[Dict[str, str]]:
        """Get list of supported models from all configs"""
        models = []
        configs = self.get_all_configs()
        
        for config_name, config_info in configs.items():
            model_info = config_info['model_info']
            if model_info['name_or_path'] != 'Unknown':
                models.append({
                    'config_name': config_name,
                    'model_path': model_info['name_or_path'],
                    'architecture': model_info['arch'],
                    'is_flux': model_info['is_flux'],
                    'is_xl': model_info['is_xl'],
                    'is_v2': model_info['is_v2']
                })
        
        return models
    
    def validate_config(self, config_data: Dict) -> List[str]:
        """Validate configuration data and return list of errors"""
        errors = []
        
        if 'job' not in config_data:
            errors.append("Missing 'job' field")
        
        if 'config' not in config_data:
            errors.append("Missing 'config' section")
            return errors
        
        config_section = config_data['config']
        
        if 'name' not in config_section:
            errors.append("Missing 'config.name' field")
        
        if 'process' not in config_section:
            errors.append("Missing 'config.process' field")
            return errors
        
        if not isinstance(config_section['process'], list) or len(config_section['process']) == 0:
            errors.append("'config.process' must be a non-empty list")
            return errors
        
        process = config_section['process'][0]
        
        if 'type' not in process:
            errors.append("Missing 'config.process[0].type' field")
        
        # Validate model section
        if 'model' in process:
            model = process['model']
            if 'name_or_path' not in model:
                errors.append("Missing 'config.process[0].model.name_or_path' field")
        
        # Validate datasets section
        if 'datasets' in process:
            datasets = process['datasets']
            if not isinstance(datasets, list) or len(datasets) == 0:
                errors.append("'config.process[0].datasets' must be a non-empty list")
            else:
                for i, dataset in enumerate(datasets):
                    if 'folder_path' not in dataset:
                        errors.append(f"Missing 'config.process[0].datasets[{i}].folder_path' field")
        
        return errors
