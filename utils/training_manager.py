import os
import sys
import uuid
import yaml
import shutil
import threading
import time
from pathlib import Path
from typing import Dict, Optional, Callable
from datetime import datetime

# Add the current working directory to the Python path
sys.path.insert(0, os.getcwd())

from toolkit.job import get_job
from slugify import slugify

class TrainingManager:
    """Manages LoRA training jobs and status tracking"""
    
    def __init__(self, dataset_folder: str = 'datasets', output_folder: str = 'output', tmp_folder: str = 'tmp'):
        self.dataset_folder = Path(dataset_folder)
        self.output_folder = Path(output_folder)
        self.tmp_folder = Path(tmp_folder)
        
        # Create directories if they don't exist
        for folder in [self.dataset_folder, self.output_folder, self.tmp_folder]:
            folder.mkdir(exist_ok=True)
        
        # Training status tracking
        self.current_job = None
        self.training_status = {
            'is_training': False,
            'progress': 0,
            'message': '',
            'job_id': None,
            'error': None,
            'start_time': None,
            'estimated_completion': None,
            'current_step': 0,
            'total_steps': 0
        }
        
        # Callbacks
        self.progress_callback = None
        self.completion_callback = None
        self.error_callback = None
    
    def set_callbacks(self, 
                     progress_callback: Optional[Callable] = None,
                     completion_callback: Optional[Callable] = None,
                     error_callback: Optional[Callable] = None):
        """Set callback functions for training events"""
        self.progress_callback = progress_callback
        self.completion_callback = completion_callback
        self.error_callback = error_callback
    
    def create_dataset(self, session_folder: str, captions: Dict[str, str], concept_sentence: str = '') -> str:
        """Create a dataset from uploaded files and captions"""
        dataset_id = str(uuid.uuid4())
        dataset_path = self.dataset_folder / dataset_id
        dataset_path.mkdir(exist_ok=True)
        
        session_path = Path(session_folder)
        image_count = 0
        
        # Copy images and create caption files
        for filename in os.listdir(session_path):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                # Copy image
                src_path = session_path / filename
                dst_path = dataset_path / filename
                shutil.copy2(src_path, dst_path)
                
                # Create caption file
                caption_filename = filename.rsplit('.', 1)[0] + '.txt'
                caption_path = dataset_path / caption_filename
                
                # Get caption text
                caption_text = captions.get(filename, '[trigger]' if concept_sentence else 'a photo')
                
                # Replace [trigger] with actual concept sentence
                if concept_sentence and '[trigger]' in caption_text:
                    caption_text = caption_text.replace('[trigger]', concept_sentence)
                
                with open(caption_path, 'w', encoding='utf-8') as f:
                    f.write(caption_text)
                
                image_count += 1
        
        if image_count == 0:
            raise ValueError("No images found in session folder")
        
        return str(dataset_path)
    
    def prepare_config(self, base_config_path: str, training_params: Dict) -> str:
        """Prepare training configuration from base config and parameters"""
        with open(base_config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Update config with training parameters
        slugged_name = slugify(training_params['lora_name'])
        config['config']['name'] = slugged_name
        
        # Update process configuration
        process_config = config['config']['process'][0]
        
        # Update dataset path
        if 'datasets' in process_config and len(process_config['datasets']) > 0:
            process_config['datasets'][0]['folder_path'] = training_params['dataset_path']
        
        # Update training parameters
        if 'train' in process_config:
            train_config = process_config['train']
            train_config['steps'] = int(training_params.get('steps', 1000))
            train_config['lr'] = float(training_params.get('learning_rate', 1e-4))
            train_config['batch_size'] = int(training_params.get('batch_size', 1))
        
        # Update network parameters
        if 'network' in process_config:
            network_config = process_config['network']
            rank = int(training_params.get('rank', 16))
            network_config['linear'] = rank
            network_config['linear_alpha'] = rank
        
        # Set trigger word
        if training_params.get('concept_sentence'):
            process_config['trigger_word'] = training_params['concept_sentence']
        
        # Update save configuration
        if 'save' in process_config:
            save_config = process_config['save']
            # Set output folder
            save_config['save_every'] = int(training_params.get('save_every', 250))
        
        # Save modified config
        job_id = str(uuid.uuid4())
        config_path = self.tmp_folder / f"{job_id}-{slugged_name}.yaml"
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False)
        
        return str(config_path), job_id
    
    def start_training(self, config_path: str, job_id: str) -> bool:
        """Start training in a background thread"""
        if self.training_status['is_training']:
            raise RuntimeError("Training already in progress")
        
        # Update training status
        self.training_status.update({
            'is_training': True,
            'progress': 0,
            'message': 'Initializing training...',
            'job_id': job_id,
            'error': None,
            'start_time': datetime.now(),
            'current_step': 0,
            'total_steps': 0
        })
        
        def run_training():
            try:
                # Load and run the job
                job = get_job(config_path)
                self.current_job = job
                
                # Extract total steps for progress tracking
                if hasattr(job, 'config') and 'process' in job.config:
                    process = job.config['process'][0]
                    if 'train' in process:
                        self.training_status['total_steps'] = process['train'].get('steps', 1000)
                
                self.training_status['message'] = 'Training started...'
                if self.progress_callback:
                    self.progress_callback(self.training_status)
                
                # Run the training job
                job.run()
                job.cleanup()
                
                # Training completed successfully
                self.training_status.update({
                    'is_training': False,
                    'progress': 100,
                    'message': 'Training completed successfully!',
                    'error': None
                })
                
                if self.completion_callback:
                    self.completion_callback(self.training_status)
                
            except Exception as e:
                error_msg = str(e)
                self.training_status.update({
                    'is_training': False,
                    'progress': 0,
                    'message': 'Training failed',
                    'error': error_msg
                })
                
                if self.error_callback:
                    self.error_callback(self.training_status, e)
                
                print(f"Training error: {error_msg}")
            
            finally:
                self.current_job = None
        
        # Start training thread
        training_thread = threading.Thread(target=run_training, daemon=True)
        training_thread.start()
        
        return True
    
    def stop_training(self) -> bool:
        """Stop current training (simplified implementation)"""
        if not self.training_status['is_training']:
            return False
        
        # In a real implementation, you would need proper process management
        # For now, we just update the status
        self.training_status.update({
            'is_training': False,
            'progress': 0,
            'message': 'Training stopped by user',
            'error': None
        })
        
        self.current_job = None
        return True
    
    def get_status(self) -> Dict:
        """Get current training status"""
        return self.training_status.copy()
    
    def update_progress(self, current_step: int, message: str = ''):
        """Update training progress (called by training process)"""
        if self.training_status['total_steps'] > 0:
            progress = min(100, int((current_step / self.training_status['total_steps']) * 100))
            self.training_status['progress'] = progress
        
        self.training_status['current_step'] = current_step
        if message:
            self.training_status['message'] = message
        
        # Estimate completion time
        if self.training_status['start_time'] and current_step > 0:
            elapsed = datetime.now() - self.training_status['start_time']
            if self.training_status['total_steps'] > 0:
                estimated_total = elapsed * (self.training_status['total_steps'] / current_step)
                self.training_status['estimated_completion'] = self.training_status['start_time'] + estimated_total
        
        if self.progress_callback:
            self.progress_callback(self.training_status)
    
    def cleanup_old_files(self, max_age_days: int = 7):
        """Clean up old temporary files and datasets"""
        cutoff_time = time.time() - (max_age_days * 24 * 60 * 60)
        
        # Clean up tmp folder
        for file_path in self.tmp_folder.glob('*.yaml'):
            if file_path.stat().st_mtime < cutoff_time:
                try:
                    file_path.unlink()
                    print(f"Cleaned up old config: {file_path}")
                except Exception as e:
                    print(f"Error cleaning up {file_path}: {e}")
        
        # Clean up old datasets (be careful with this)
        for dataset_path in self.dataset_folder.iterdir():
            if dataset_path.is_dir() and dataset_path.stat().st_mtime < cutoff_time:
                try:
                    shutil.rmtree(dataset_path)
                    print(f"Cleaned up old dataset: {dataset_path}")
                except Exception as e:
                    print(f"Error cleaning up {dataset_path}: {e}")
    
    def get_training_history(self) -> list:
        """Get history of completed training jobs"""
        # This would typically read from a database or log files
        # For now, return empty list
        return []
    
    def validate_training_params(self, params: Dict) -> list:
        """Validate training parameters and return list of errors"""
        errors = []
        
        required_params = ['lora_name', 'config_name', 'dataset_path']
        for param in required_params:
            if not params.get(param):
                errors.append(f"Missing required parameter: {param}")
        
        # Validate numeric parameters
        numeric_params = {
            'steps': (100, 10000),
            'learning_rate': (1e-6, 1e-2),
            'rank': (4, 128),
            'batch_size': (1, 16)
        }
        
        for param, (min_val, max_val) in numeric_params.items():
            if param in params:
                try:
                    value = float(params[param])
                    if not (min_val <= value <= max_val):
                        errors.append(f"{param} must be between {min_val} and {max_val}")
                except (ValueError, TypeError):
                    errors.append(f"{param} must be a valid number")
        
        return errors
