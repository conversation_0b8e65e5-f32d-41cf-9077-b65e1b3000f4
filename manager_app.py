"""
LoRA Training Studio - Manager Application
Comprehensive admin dashboard with unlimited access and full system control
"""

import os
import sys
import uuid
import yaml
import shutil
import subprocess
import threading
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_from_directory, send_file
from flask_cors import CORS
from werkzeug.utils import secure_filename
from PIL import Image
from slugify import slugify

# Add the current working directory to the Python path
sys.path.insert(0, os.getcwd())

# Import billing system
try:
    from billing_manager import BillingManager
    from pricing_engine import TokenPricingEngine, ServiceTier, TOKEN_PACKAGES
    from auth_system import AdvancedAuthSystem
    BILLING_ENABLED = True
except ImportError as e:
    print(f"Billing system not available: {e}")
    BILLING_ENABLED = False

app = Flask(__name__)
app.secret_key = 'manager-super-secret-key-change-this'
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
DATASET_FOLDER = 'datasets'
OUTPUT_FOLDER = 'output'
CONFIG_FOLDER = 'config'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'txt'}
MAX_CONTENT_LENGTH = 500 * 1024 * 1024  # 500MB max file size

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Create necessary directories
for folder in [UPLOAD_FOLDER, DATASET_FOLDER, OUTPUT_FOLDER]:
    os.makedirs(folder, exist_ok=True)

# Initialize systems
billing_manager = None
pricing_engine = None
auth_system = None

if BILLING_ENABLED:
    billing_manager = BillingManager()
    pricing_engine = TokenPricingEngine()
    auth_system = AdvancedAuthSystem()
    print("✅ Manager systems initialized")

# Manager credentials (in production, use environment variables)
MANAGER_CREDENTIALS = {
    'username': os.getenv('MANAGER_USERNAME', 'admin'),
    'password': os.getenv('MANAGER_PASSWORD', 'admin123'),
    'email': os.getenv('MANAGER_EMAIL', '<EMAIL>')
}

# Global training status
training_status = {
    'is_training': False,
    'progress': 0,
    'message': 'Ready',
    'job_id': None,
    'error': None
}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def manager_dashboard():
    """Manager dashboard with full system overview"""
    return render_template('manager_dashboard.html')

@app.route('/api/manager/auth', methods=['POST'])
def manager_auth():
    """Manager authentication"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if username == MANAGER_CREDENTIALS['username'] and password == MANAGER_CREDENTIALS['password']:
        return jsonify({
            'success': True,
            'message': 'Manager authenticated',
            'permissions': ['unlimited_tokens', 'user_management', 'system_control', 'analytics']
        })
    else:
        return jsonify({
            'success': False,
            'error': 'Invalid manager credentials'
        }), 401

@app.route('/api/manager/stats')
def manager_stats():
    """Get comprehensive system statistics"""
    if not BILLING_ENABLED:
        return jsonify({'error': 'Billing system not available'}), 503
    
    conn = sqlite3.connect(billing_manager.db_path)
    cursor = conn.cursor()
    
    # User statistics
    cursor.execute('SELECT COUNT(*) FROM users')
    total_users = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM users WHERE email_verified = TRUE')
    verified_users = cursor.fetchone()[0]
    
    cursor.execute('SELECT tier, COUNT(*) FROM users GROUP BY tier')
    tier_distribution = dict(cursor.fetchall())
    
    # Revenue statistics
    cursor.execute('SELECT SUM(amount_paid) FROM token_purchases')
    total_revenue = cursor.fetchone()[0] or 0
    
    cursor.execute('SELECT SUM(tokens_purchased + bonus_tokens) FROM token_purchases')
    total_tokens_sold = cursor.fetchone()[0] or 0
    
    # Usage statistics
    cursor.execute('SELECT COUNT(*) FROM usage_logs WHERE service_type = "training"')
    total_training_jobs = cursor.fetchone()[0]
    
    cursor.execute('SELECT SUM(tokens_consumed) FROM usage_logs')
    total_tokens_consumed = cursor.fetchone()[0] or 0
    
    # Recent activity (last 7 days)
    seven_days_ago = datetime.now() - timedelta(days=7)
    cursor.execute('SELECT COUNT(*) FROM users WHERE created_at > ?', (seven_days_ago,))
    new_users_week = cursor.fetchone()[0]
    
    cursor.execute('SELECT SUM(amount_paid) FROM token_purchases WHERE created_at > ?', (seven_days_ago,))
    revenue_week = cursor.fetchone()[0] or 0
    
    conn.close()
    
    # System statistics
    disk_usage = get_disk_usage()
    model_count = len([f for f in os.listdir(OUTPUT_FOLDER) if os.path.isdir(os.path.join(OUTPUT_FOLDER, f))])
    
    return jsonify({
        'users': {
            'total': total_users,
            'verified': verified_users,
            'new_this_week': new_users_week,
            'tier_distribution': tier_distribution
        },
        'revenue': {
            'total': total_revenue,
            'this_week': revenue_week,
            'tokens_sold': total_tokens_sold,
            'tokens_consumed': total_tokens_consumed
        },
        'usage': {
            'training_jobs': total_training_jobs,
            'models_created': model_count,
            'disk_usage': disk_usage
        },
        'system': {
            'billing_enabled': BILLING_ENABLED,
            'training_active': training_status['is_training']
        }
    })

@app.route('/api/manager/users')
def manager_users():
    """Get all users with detailed information"""
    if not BILLING_ENABLED:
        return jsonify({'error': 'Billing system not available'}), 503
    
    conn = sqlite3.connect(billing_manager.db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT user_id, email, tier, token_balance, total_spent, 
               email_verified, full_name, created_at
        FROM users ORDER BY created_at DESC
    ''')
    
    users = []
    for row in cursor.fetchall():
        user_id, email, tier, balance, spent, verified, name, created = row
        
        # Get recent usage
        cursor.execute('''
            SELECT COUNT(*), SUM(tokens_consumed) 
            FROM usage_logs 
            WHERE user_id = ? AND created_at > ?
        ''', (user_id, datetime.now() - timedelta(days=30)))
        
        usage_stats = cursor.fetchone()
        jobs_count = usage_stats[0] or 0
        tokens_used = usage_stats[1] or 0
        
        users.append({
            'user_id': user_id,
            'email': email,
            'name': name or 'N/A',
            'tier': tier,
            'token_balance': balance,
            'total_spent': spent,
            'email_verified': bool(verified),
            'created_at': created,
            'recent_jobs': jobs_count,
            'recent_tokens': tokens_used
        })
    
    conn.close()
    return jsonify({'users': users})

@app.route('/api/manager/user/<user_id>/tokens', methods=['POST'])
def manager_add_tokens(user_id):
    """Add tokens to user account (manager privilege)"""
    if not BILLING_ENABLED:
        return jsonify({'error': 'Billing system not available'}), 503
    
    data = request.get_json()
    amount = data.get('amount', 0)
    reason = data.get('reason', 'Manager credit')
    
    if amount <= 0:
        return jsonify({'error': 'Invalid amount'}), 400
    
    result = billing_manager.add_tokens(user_id, amount, f"Manager credit: {reason}")
    return jsonify(result)

@app.route('/api/manager/user/<user_id>/tier', methods=['POST'])
def manager_update_tier(user_id):
    """Update user tier (manager privilege)"""
    if not BILLING_ENABLED:
        return jsonify({'error': 'Billing system not available'}), 503
    
    data = request.get_json()
    new_tier = data.get('tier')
    
    if new_tier not in ['starter', 'professional', 'enterprise']:
        return jsonify({'error': 'Invalid tier'}), 400
    
    conn = sqlite3.connect(billing_manager.db_path)
    cursor = conn.cursor()
    
    cursor.execute('UPDATE users SET tier = ? WHERE user_id = ?', (new_tier, user_id))
    conn.commit()
    conn.close()
    
    return jsonify({'success': True, 'message': f'User tier updated to {new_tier}'})

@app.route('/api/manager/training/unlimited', methods=['POST'])
def manager_unlimited_training():
    """Start training with unlimited tokens (manager privilege)"""
    global training_status
    
    if training_status['is_training']:
        return jsonify({'error': 'Training already in progress'}), 400
    
    data = request.get_json()
    
    # Extract parameters (same as regular training but no token deduction)
    session_id = data.get('session_id')
    config_name = data.get('config_name')
    lora_name = data.get('lora_name')
    concept_sentence = data.get('concept_sentence', '')
    sample_prompt = data.get('sample_prompt', '')
    captions = data.get('captions', {})
    
    # Validation
    if not all([session_id, config_name, lora_name]):
        return jsonify({'error': 'Missing required parameters'}), 400
    
    session_folder = os.path.join(UPLOAD_FOLDER, session_id)
    if not os.path.exists(session_folder):
        return jsonify({'error': 'Session not found'}), 404
    
    try:
        # Create dataset
        dataset_path = create_dataset_folder(session_folder, captions, concept_sentence)
        
        # Get base config path
        base_config_path = os.path.join(CONFIG_FOLDER, f"{config_name}.yaml")
        if not os.path.exists(base_config_path):
            return jsonify({'error': 'Configuration file not found'}), 404
        
        # Prepare training configuration
        config_path, job_id = prepare_training_config(
            base_config_path,
            dataset_path,
            lora_name,
            concept_sentence,
            sample_prompt,
            steps=data.get('steps', 1000),
            learning_rate=data.get('learning_rate', 1e-4),
            rank=data.get('rank', 16),
            batch_size=data.get('batch_size', 1)
        )
        
        # Update training status
        training_status.update({
            'is_training': True,
            'progress': 0,
            'message': 'Starting unlimited training...',
            'job_id': job_id,
            'error': None
        })
        
        # Start training in background thread
        def run_unlimited_training():
            try:
                cmd = [sys.executable, 'run.py', config_path]
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=os.getcwd()
                )
                
                _, stderr = process.communicate()
                
                if process.returncode == 0:
                    training_status.update({
                        'is_training': False,
                        'progress': 100,
                        'message': 'Unlimited training completed successfully!',
                        'error': None
                    })
                else:
                    training_status.update({
                        'is_training': False,
                        'progress': 0,
                        'message': 'Training failed',
                        'error': stderr or 'Unknown error occurred'
                    })
                    
            except Exception as e:
                training_status.update({
                    'is_training': False,
                    'progress': 0,
                    'message': 'Training failed',
                    'error': str(e)
                })
        
        thread = threading.Thread(target=run_unlimited_training)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'message': 'Unlimited training started',
            'job_id': job_id,
            'note': 'No tokens deducted - Manager privilege'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/manager/system/backup', methods=['POST'])
def manager_backup_system():
    """Create system backup"""
    try:
        backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_path = f"backups/{backup_name}"
        os.makedirs(backup_path, exist_ok=True)

        # Backup database
        if BILLING_ENABLED:
            shutil.copy2(billing_manager.db_path, f"{backup_path}/billing.db")

        # Backup configs
        if os.path.exists(CONFIG_FOLDER):
            shutil.copytree(CONFIG_FOLDER, f"{backup_path}/config")

        # Backup models (only metadata, not full models due to size)
        models_info = []
        if os.path.exists(OUTPUT_FOLDER):
            for model_dir in os.listdir(OUTPUT_FOLDER):
                model_path = os.path.join(OUTPUT_FOLDER, model_dir)
                if os.path.isdir(model_path):
                    models_info.append({
                        'name': model_dir,
                        'created': os.path.getctime(model_path),
                        'size': get_folder_size(model_path)
                    })

        with open(f"{backup_path}/models_info.json", 'w') as f:
            import json
            json.dump(models_info, f, indent=2)

        return jsonify({
            'success': True,
            'backup_name': backup_name,
            'backup_path': backup_path
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Include all the regular training endpoints for manager use
@app.route('/upload', methods=['POST'])
def upload_files():
    """Handle file uploads for manager"""
    if 'files' not in request.files:
        return jsonify({'error': 'No files provided'}), 400

    files = request.files.getlist('files')
    if not files or files[0].filename == '':
        return jsonify({'error': 'No files selected'}), 400

    # Create unique upload session
    session_id = str(uuid.uuid4())
    session_folder = os.path.join(UPLOAD_FOLDER, session_id)
    os.makedirs(session_folder, exist_ok=True)

    uploaded_files = []

    for file in files:
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            file_path = os.path.join(session_folder, filename)
            file.save(file_path)

            # Get file info
            file_info = {
                'filename': filename,
                'path': file_path,
                'size': os.path.getsize(file_path),
                'type': 'image' if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')) else 'video' if filename.lower().endswith(('.mp4', '.avi', '.mov')) else 'text'
            }

            # If it's an image, get dimensions
            if file_info['type'] == 'image':
                try:
                    with Image.open(file_path) as img:
                        file_info['width'], file_info['height'] = img.size
                except Exception as e:
                    print(f"Error getting image dimensions: {e}")

            uploaded_files.append(file_info)

    return jsonify({
        'session_id': session_id,
        'files': uploaded_files,
        'message': f'Successfully uploaded {len(uploaded_files)} files'
    })

@app.route('/api/training/status')
def training_status_endpoint():
    """Get current training status"""
    return jsonify(training_status)

@app.route('/uploads/<session_id>/<filename>')
def uploaded_file(session_id, filename):
    """Serve uploaded files"""
    return send_from_directory(os.path.join(UPLOAD_FOLDER, session_id), filename)

def get_disk_usage():
    """Get disk usage statistics"""
    total_size = 0
    for folder in [UPLOAD_FOLDER, DATASET_FOLDER, OUTPUT_FOLDER]:
        if os.path.exists(folder):
            total_size += get_folder_size(folder)
    return total_size

def get_folder_size(folder_path):
    """Calculate total size of a folder"""
    total_size = 0
    for dirpath, _, filenames in os.walk(folder_path):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            if os.path.exists(file_path):
                total_size += os.path.getsize(file_path)
    return total_size

def create_dataset_folder(session_folder, captions, concept_sentence=''):
    """Create a dataset folder from uploaded files and captions"""
    dataset_id = str(uuid.uuid4())
    dataset_path = os.path.join(DATASET_FOLDER, dataset_id)
    os.makedirs(dataset_path, exist_ok=True)

    image_count = 0
    for filename in os.listdir(session_folder):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
            # Copy image
            src_path = os.path.join(session_folder, filename)
            dst_path = os.path.join(dataset_path, filename)
            shutil.copy2(src_path, dst_path)

            # Create caption file
            caption_filename = os.path.splitext(filename)[0] + '.txt'
            caption_path = os.path.join(dataset_path, caption_filename)

            caption_text = captions.get(filename, '[trigger]' if concept_sentence else 'a photo')

            # Replace [trigger] with actual concept sentence
            if concept_sentence and '[trigger]' in caption_text:
                caption_text = caption_text.replace('[trigger]', concept_sentence)

            with open(caption_path, 'w') as f:
                f.write(caption_text)

            image_count += 1

    if image_count == 0:
        raise ValueError("No images found in session folder")

    return dataset_path

def prepare_training_config(base_config_path, dataset_path, lora_name, concept_sentence='', sample_prompt='', **params):
    """Prepare a training configuration file with enhanced sampling"""
    with open(base_config_path, 'r') as f:
        config = yaml.safe_load(f)

    # Update config with user parameters
    slugged_name = slugify(lora_name)
    config['config']['name'] = slugged_name

    # Update process configuration
    process_config = config['config']['process'][0]

    # Update dataset path
    if 'datasets' in process_config and len(process_config['datasets']) > 0:
        process_config['datasets'][0]['folder_path'] = dataset_path

    # Update training parameters if provided
    if 'train' in process_config:
        train_config = process_config['train']
        if 'steps' in params:
            train_config['steps'] = int(params['steps'])
        if 'learning_rate' in params:
            train_config['lr'] = float(params['learning_rate'])
        if 'batch_size' in params:
            train_config['batch_size'] = int(params['batch_size'])

    # Update network parameters if provided
    if 'network' in process_config and 'rank' in params:
        network_config = process_config['network']
        rank = int(params['rank'])
        network_config['linear'] = rank
        network_config['linear_alpha'] = rank

    # Enhanced sampling configuration
    if 'sample' in process_config:
        sample_config = process_config['sample']

        # Set custom sample prompt or use default
        if sample_prompt:
            # Use custom prompt with trigger word replacement
            if concept_sentence and '[trigger]' in sample_prompt:
                final_prompt = sample_prompt.replace('[trigger]', concept_sentence)
            else:
                final_prompt = sample_prompt

            # Generate 2 variations of the prompt for testing
            sample_config['prompts'] = [
                final_prompt,
                final_prompt + ", high quality, detailed"
            ]
        else:
            # Default prompts with trigger word
            default_prompts = [
                f"a photo of {concept_sentence}, high quality" if concept_sentence else "a high quality photo",
                f"{concept_sentence} in a professional setting" if concept_sentence else "professional photo"
            ]
            sample_config['prompts'] = default_prompts

        # Ensure sampling every 250 steps
        sample_config['sample_every'] = 250
        sample_config['walk_seed'] = True  # Use different seeds for variety

    # Set trigger word
    if concept_sentence:
        process_config['trigger_word'] = concept_sentence

    # Save modified config
    job_id = str(uuid.uuid4())
    config_path = os.path.join('tmp', f"{job_id}-{slugged_name}.yaml")
    os.makedirs('tmp', exist_ok=True)

    with open(config_path, 'w') as f:
        yaml.dump(config, f)

    return config_path, job_id

if __name__ == '__main__':
    print("🚀 LoRA Training Studio - Manager Application")
    print("📊 Full system access with unlimited tokens")
    print("🔧 Manager credentials:")
    print(f"   Username: {MANAGER_CREDENTIALS['username']}")
    print(f"   Password: {MANAGER_CREDENTIALS['password']}")
    print("🌐 Starting manager dashboard...")
    
    app.run(debug=True, host='0.0.0.0', port=5007)
