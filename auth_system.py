"""
Professional Authentication System with Gmail Support
Handles OAuth2, email verification, and secure user management
"""

import os
import smtplib
import secrets
import hashlib
import sqlite3
import jwt
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON>ipart
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Optional
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build

class EmailService:
    """Professional email service for authentication"""
    
    def __init__(self, smtp_server="smtp.gmail.com", smtp_port=587):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.email_user = os.getenv('EMAIL_USER', '<EMAIL>')
        self.email_password = os.getenv('EMAIL_PASSWORD', 'your-app-password')
    
    def send_verification_email(self, to_email: str, verification_code: str, user_name: str = "") -> bool:
        """Send professional verification email"""
        try:
            msg = MIMEMultipart('alternative')
            msg['From'] = f"LoRA Training Studio <{self.email_user}>"
            msg['To'] = to_email
            msg['Subject'] = "Verify Your LoRA Training Studio Account"
            
            # HTML email template
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: 'Inter', Arial, sans-serif; margin: 0; padding: 0; background: #0a0a0f; color: #ffffff; }}
                    .container {{ max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); }}
                    .header {{ background: linear-gradient(135deg, #00d4ff 0%, #090979 100%); padding: 30px; text-align: center; }}
                    .content {{ padding: 40px 30px; }}
                    .verification-code {{ background: rgba(0, 212, 255, 0.1); border: 2px solid #00d4ff; border-radius: 12px; padding: 20px; text-align: center; margin: 30px 0; }}
                    .code {{ font-size: 32px; font-weight: bold; color: #00d4ff; letter-spacing: 4px; }}
                    .button {{ background: linear-gradient(135deg, #00d4ff 0%, #090979 100%); color: white; padding: 15px 30px; border-radius: 8px; text-decoration: none; display: inline-block; margin: 20px 0; }}
                    .footer {{ background: rgba(255, 255, 255, 0.05); padding: 20px; text-align: center; font-size: 12px; color: #b8c5d1; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1 style="margin: 0; color: white;">LoRA Training Studio</h1>
                        <p style="margin: 10px 0 0 0; color: rgba(255,255,255,0.8);">Professional AI Model Training Platform</p>
                    </div>
                    <div class="content">
                        <h2>Welcome{f", {user_name}" if user_name else ""}!</h2>
                        <p>Thank you for joining LoRA Training Studio, the premier platform for professional AI model training.</p>
                        
                        <p>To complete your registration and start training world-class LoRA models, please verify your email address using the code below:</p>
                        
                        <div class="verification-code">
                            <p style="margin: 0 0 10px 0; font-size: 14px; color: #b8c5d1;">Your Verification Code</p>
                            <div class="code">{verification_code}</div>
                        </div>
                        
                        <p>This code will expire in 15 minutes for security purposes.</p>
                        
                        <p><strong>What's Next?</strong></p>
                        <ul style="color: #b8c5d1;">
                            <li>Complete email verification</li>
                            <li>Receive 100 welcome tokens</li>
                            <li>Start training your first LoRA model</li>
                            <li>Access our professional-grade infrastructure</li>
                        </ul>
                        
                        <p>If you didn't create this account, please ignore this email.</p>
                    </div>
                    <div class="footer">
                        <p>© 2024 LoRA Training Studio. All rights reserved.</p>
                        <p>Professional AI Model Training • Enterprise-Grade Infrastructure • 24/7 Support</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Plain text version
            text_content = f"""
            Welcome to LoRA Training Studio!
            
            Thank you for joining our professional AI model training platform.
            
            Your verification code is: {verification_code}
            
            This code will expire in 15 minutes.
            
            If you didn't create this account, please ignore this email.
            
            © 2024 LoRA Training Studio
            """
            
            msg.attach(MIMEText(text_content, 'plain'))
            msg.attach(MIMEText(html_content, 'html'))
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.email_user, self.email_password)
                server.send_message(msg)
            
            return True
            
        except Exception as e:
            print(f"Failed to send verification email: {e}")
            return False
    
    def send_password_reset_email(self, to_email: str, reset_token: str, user_name: str = "") -> bool:
        """Send password reset email"""
        try:
            reset_link = f"http://localhost:5006/reset-password?token={reset_token}"
            
            msg = MIMEMultipart('alternative')
            msg['From'] = f"LoRA Training Studio <{self.email_user}>"
            msg['To'] = to_email
            msg['Subject'] = "Reset Your LoRA Training Studio Password"
            
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: 'Inter', Arial, sans-serif; margin: 0; padding: 0; background: #0a0a0f; color: #ffffff; }}
                    .container {{ max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); }}
                    .header {{ background: linear-gradient(135deg, #00d4ff 0%, #090979 100%); padding: 30px; text-align: center; }}
                    .content {{ padding: 40px 30px; }}
                    .button {{ background: linear-gradient(135deg, #00d4ff 0%, #090979 100%); color: white; padding: 15px 30px; border-radius: 8px; text-decoration: none; display: inline-block; margin: 20px 0; }}
                    .footer {{ background: rgba(255, 255, 255, 0.05); padding: 20px; text-align: center; font-size: 12px; color: #b8c5d1; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1 style="margin: 0; color: white;">LoRA Training Studio</h1>
                    </div>
                    <div class="content">
                        <h2>Password Reset Request</h2>
                        <p>Hi{f" {user_name}" if user_name else ""},</p>
                        <p>We received a request to reset your password for your LoRA Training Studio account.</p>
                        
                        <a href="{reset_link}" class="button">Reset Password</a>
                        
                        <p>This link will expire in 1 hour for security purposes.</p>
                        <p>If you didn't request this reset, please ignore this email.</p>
                    </div>
                    <div class="footer">
                        <p>© 2024 LoRA Training Studio. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            text_content = f"""
            Password Reset Request
            
            Hi{f" {user_name}" if user_name else ""},
            
            We received a request to reset your password.
            
            Reset link: {reset_link}
            
            This link will expire in 1 hour.
            
            © 2024 LoRA Training Studio
            """
            
            msg.attach(MIMEText(text_content, 'plain'))
            msg.attach(MIMEText(html_content, 'html'))
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.email_user, self.email_password)
                server.send_message(msg)
            
            return True
            
        except Exception as e:
            print(f"Failed to send password reset email: {e}")
            return False

class GoogleAuthService:
    """Google OAuth2 authentication service"""
    
    def __init__(self):
        self.client_id = os.getenv('GOOGLE_CLIENT_ID')
        self.client_secret = os.getenv('GOOGLE_CLIENT_SECRET')
        self.redirect_uri = os.getenv('GOOGLE_REDIRECT_URI', 'http://localhost:5006/auth/google/callback')
        
        self.scopes = [
            'openid',
            'email',
            'profile'
        ]
    
    def get_authorization_url(self) -> str:
        """Get Google OAuth2 authorization URL"""
        if not self.client_id or not self.client_secret:
            raise ValueError("Google OAuth credentials not configured")
        
        flow = Flow.from_client_config(
            {
                "web": {
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [self.redirect_uri]
                }
            },
            scopes=self.scopes
        )
        flow.redirect_uri = self.redirect_uri
        
        authorization_url, state = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true'
        )
        
        return authorization_url, state
    
    def exchange_code_for_token(self, code: str, state: str) -> Dict:
        """Exchange authorization code for user info"""
        try:
            flow = Flow.from_client_config(
                {
                    "web": {
                        "client_id": self.client_id,
                        "client_secret": self.client_secret,
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "redirect_uris": [self.redirect_uri]
                    }
                },
                scopes=self.scopes,
                state=state
            )
            flow.redirect_uri = self.redirect_uri
            
            # Exchange code for token
            flow.fetch_token(code=code)
            
            # Get user info
            credentials = flow.credentials
            service = build('oauth2', 'v2', credentials=credentials)
            user_info = service.userinfo().get().execute()
            
            return {
                'success': True,
                'email': user_info.get('email'),
                'name': user_info.get('name'),
                'picture': user_info.get('picture'),
                'verified_email': user_info.get('verified_email', False)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

class AdvancedAuthSystem:
    """Advanced authentication system with email verification and OAuth"""
    
    def __init__(self, db_path: str = "billing.db", secret_key: str = "your-secret-key"):
        self.db_path = db_path
        self.secret_key = secret_key
        self.email_service = EmailService()
        self.google_auth = GoogleAuthService()
        self.init_auth_tables()
    
    def init_auth_tables(self):
        """Initialize authentication tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Email verification table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS email_verifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT,
                verification_code TEXT,
                expires_at TIMESTAMP,
                verified BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Password reset table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS password_resets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT,
                reset_token TEXT,
                expires_at TIMESTAMP,
                used BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # OAuth accounts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS oauth_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT,
                provider TEXT,
                provider_id TEXT,
                email TEXT,
                name TEXT,
                picture TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        ''')
        
        # Add email verification status to users table (if not exists)
        try:
            cursor.execute('''
                ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE
            ''')
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Add name and profile picture to users table (if not exists)
        try:
            cursor.execute('''
                ALTER TABLE users ADD COLUMN full_name TEXT
            ''')
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            cursor.execute('''
                ALTER TABLE users ADD COLUMN profile_picture TEXT
            ''')
        except sqlite3.OperationalError:
            pass  # Column already exists
        
        conn.commit()
        conn.close()
    
    def generate_verification_code(self) -> str:
        """Generate 6-digit verification code"""
        return f"{secrets.randbelow(900000) + 100000:06d}"
    
    def send_verification_email(self, email: str, user_name: str = "") -> Dict:
        """Send email verification code"""
        verification_code = self.generate_verification_code()
        expires_at = datetime.now() + timedelta(minutes=15)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Store verification code
        cursor.execute('''
            INSERT INTO email_verifications (email, verification_code, expires_at)
            VALUES (?, ?, ?)
        ''', (email, verification_code, expires_at))
        
        conn.commit()
        conn.close()
        
        # Send email
        success = self.email_service.send_verification_email(email, verification_code, user_name)
        
        return {
            'success': success,
            'message': 'Verification email sent' if success else 'Failed to send verification email'
        }
    
    def verify_email_code(self, email: str, code: str) -> Dict:
        """Verify email with code"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id FROM email_verifications 
            WHERE email = ? AND verification_code = ? 
            AND expires_at > ? AND verified = FALSE
            ORDER BY created_at DESC LIMIT 1
        ''', (email, code, datetime.now()))
        
        verification = cursor.fetchone()
        
        if verification:
            # Mark as verified
            cursor.execute('''
                UPDATE email_verifications SET verified = TRUE WHERE id = ?
            ''', (verification[0],))
            
            # Update user email verification status
            cursor.execute('''
                UPDATE users SET email_verified = TRUE WHERE email = ?
            ''', (email,))
            
            conn.commit()
            conn.close()
            
            return {
                'success': True,
                'message': 'Email verified successfully'
            }
        else:
            conn.close()
            return {
                'success': False,
                'error': 'Invalid or expired verification code'
            }
