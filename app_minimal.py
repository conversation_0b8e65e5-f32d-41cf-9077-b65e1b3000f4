#!/usr/bin/env python3
"""
Minimal Flask Application for AI Toolkit LoRA Training
This version avoids heavy dependencies at startup and loads them only when needed
"""

import os
import sys
import uuid
import yaml
import shutil
import subprocess
import threading
from pathlib import Path

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename

# Try to import PIL, but don't fail if not available
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("Warning: PIL not available. Image preview may not work.")

try:
    from slugify import slugify
except ImportError:
    # Simple fallback slugify function
    import re
    def slugify(text):
        return re.sub(r'[^a-zA-Z0-9-_]', '-', text.lower()).strip('-')

# Add the current working directory to the Python path
sys.path.insert(0, os.getcwd())

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
DATASET_FOLDER = 'datasets'
OUTPUT_FOLDER = 'output'
CONFIG_FOLDER = 'config'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
MAX_CONTENT_LENGTH = 500 * 1024 * 1024  # 500MB max file size

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Create necessary directories
for folder in [UPLOAD_FOLDER, DATASET_FOLDER, OUTPUT_FOLDER]:
    os.makedirs(folder, exist_ok=True)

# Global training status
training_status = {
    'is_training': False,
    'progress': 0,
    'message': 'Ready',
    'job_id': None,
    'error': None
}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_available_configs():
    """Get all available YAML config files"""
    configs = {}
    config_path = Path(CONFIG_FOLDER)
    
    if not config_path.exists():
        return configs
    
    for config_file in config_path.glob('*.yaml'):
        try:
            with open(config_file, 'r') as f:
                config_data = yaml.safe_load(f)
                configs[config_file.stem] = {
                    'file': str(config_file),
                    'name': config_file.stem,
                    'data': config_data
                }
        except Exception as e:
            print(f"Error loading config {config_file}: {e}")
    
    return configs

@app.route('/')
def index():
    """Main page with upload and configuration interface"""
    configs = get_available_configs()
    return render_template('index.html', configs=configs)

@app.route('/api/configs')
def api_configs():
    """API endpoint to get available configurations"""
    configs = get_available_configs()
    return jsonify(configs)

@app.route('/api/config/<config_name>')
def api_config_details(config_name):
    """API endpoint to get detailed configuration"""
    try:
        config_file = Path(CONFIG_FOLDER) / f"{config_name}.yaml"
        if not config_file.exists():
            return jsonify({'error': 'Config not found'}), 404
        
        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)
        
        return jsonify({
            'name': config_name,
            'data': config_data
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/upload', methods=['POST'])
def upload_files():
    """Handle file uploads"""
    if 'files' not in request.files:
        return jsonify({'error': 'No files provided'}), 400
    
    files = request.files.getlist('files')
    if not files or files[0].filename == '':
        return jsonify({'error': 'No files selected'}), 400
    
    # Create unique upload session
    session_id = str(uuid.uuid4())
    session_folder = os.path.join(UPLOAD_FOLDER, session_id)
    os.makedirs(session_folder, exist_ok=True)
    
    uploaded_files = []
    
    for file in files:
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            file_path = os.path.join(session_folder, filename)
            file.save(file_path)
            
            # Get file info
            file_info = {
                'filename': filename,
                'path': file_path,
                'size': os.path.getsize(file_path),
                'type': 'image'
            }
            
            # If PIL is available and it's an image, get dimensions
            if PIL_AVAILABLE and filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                try:
                    with Image.open(file_path) as img:
                        file_info['width'], file_info['height'] = img.size
                except Exception as e:
                    print(f"Error getting image dimensions: {e}")
            
            uploaded_files.append(file_info)
    
    return jsonify({
        'session_id': session_id,
        'files': uploaded_files,
        'message': f'Successfully uploaded {len(uploaded_files)} files'
    })

@app.route('/api/caption', methods=['POST'])
def auto_caption():
    """Generate captions for uploaded images"""
    data = request.get_json()
    session_id = data.get('session_id')
    concept_sentence = data.get('concept_sentence', '')
    
    if not session_id:
        return jsonify({'error': 'Session ID required'}), 400
    
    session_folder = os.path.join(UPLOAD_FOLDER, session_id)
    if not os.path.exists(session_folder):
        return jsonify({'error': 'Session not found'}), 404
    
    try:
        # Try to load AI models for captioning
        try:
            import torch
            from transformers import AutoProcessor, AutoModelForCausalLM
            
            device = "cuda" if torch.cuda.is_available() else "cpu"
            torch_dtype = torch.float16
            
            model = AutoModelForCausalLM.from_pretrained(
                "multimodalart/Florence-2-large-no-flash-attn", 
                torch_dtype=torch_dtype, 
                trust_remote_code=True
            ).to(device)
            
            processor = AutoProcessor.from_pretrained(
                "multimodalart/Florence-2-large-no-flash-attn", 
                trust_remote_code=True
            )
            
            captions = {}
            
            # Process each image file
            for filename in os.listdir(session_folder):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                    image_path = os.path.join(session_folder, filename)
                    
                    try:
                        if PIL_AVAILABLE:
                            image = Image.open(image_path).convert("RGB")
                            
                            prompt = "<DETAILED_CAPTION>"
                            inputs = processor(text=prompt, images=image, return_tensors="pt").to(device, torch_dtype)
                            
                            generated_ids = model.generate(
                                input_ids=inputs["input_ids"], 
                                pixel_values=inputs["pixel_values"], 
                                max_new_tokens=1024, 
                                num_beams=3
                            )
                            
                            generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]
                            parsed_answer = processor.post_process_generation(
                                generated_text, task=prompt, image_size=(image.width, image.height)
                            )
                            
                            caption_text = parsed_answer["<DETAILED_CAPTION>"].replace("The image shows ", "")
                            
                            if concept_sentence:
                                caption_text = f"{caption_text} [trigger]"
                            
                            captions[filename] = caption_text
                        else:
                            # Fallback caption if PIL not available
                            captions[filename] = f"Image {filename}" + (" [trigger]" if concept_sentence else "")
                        
                    except Exception as e:
                        print(f"Error captioning {filename}: {e}")
                        captions[filename] = f"Image {filename}" + (" [trigger]" if concept_sentence else "")
            
            # Clean up model if loaded
            if 'model' in locals():
                model.to("cpu")
                del model
                del processor
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            
            return jsonify({
                'captions': captions,
                'message': f'Generated captions for {len(captions)} images'
            })
            
        except ImportError:
            # Fallback: generate simple captions without AI
            captions = {}
            for filename in os.listdir(session_folder):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                    base_name = os.path.splitext(filename)[0]
                    caption_text = f"A photo of {base_name.replace('_', ' ').replace('-', ' ')}"
                    if concept_sentence:
                        caption_text = f"{caption_text} [trigger]"
                    captions[filename] = caption_text
            
            return jsonify({
                'captions': captions,
                'message': f'Generated simple captions for {len(captions)} images (AI captioning not available)'
            })
        
    except Exception as e:
        return jsonify({'error': f'Captioning failed: {str(e)}'}), 500

# Include all the other functions from the main app.py
# (create_dataset_folder, prepare_training_config, start_training, etc.)

if __name__ == '__main__':
    print("🚀 Starting AI Toolkit Flask Application (Minimal Version)")
    print("📱 Access the interface at: http://localhost:5001")
    print("💡 This version loads AI models only when needed for captioning")
    app.run(debug=True, host='0.0.0.0', port=5001)
