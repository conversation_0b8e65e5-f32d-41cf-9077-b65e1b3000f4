#!/usr/bin/env python3
"""
Fix dependency issues for AI Toolkit Flask Application
This script resolves numpy/tensorflow compatibility issues
"""

import subprocess
import sys
import os

def run_command(cmd):
    """Run a command and return success status"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {cmd}")
            return True
        else:
            print(f"❌ {cmd}")
            print(f"   Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {cmd}")
        print(f"   Exception: {e}")
        return False

def main():
    print("🔧 Fixing AI Toolkit Flask App Dependencies")
    print("=" * 50)
    
    # Step 1: Uninstall problematic packages
    print("\n1️⃣ Uninstalling problematic packages...")
    uninstall_commands = [
        "pip uninstall -y tensorflow tensorflow-macos tensorflow-metal",
        "pip uninstall -y numpy",
        "pip uninstall -y transformers torch torchvision"
    ]
    
    for cmd in uninstall_commands:
        run_command(cmd)
    
    # Step 2: Install compatible numpy first
    print("\n2️⃣ Installing compatible numpy...")
    run_command("pip install 'numpy>=1.21.0,<1.25.0'")
    
    # Step 3: Install basic Flask requirements
    print("\n3️⃣ Installing Flask requirements...")
    flask_requirements = [
        "pip install flask==3.0.0",
        "pip install flask-cors==4.0.0",
        "pip install werkzeug==3.0.1",
        "pip install pillow>=10.0.0",
        "pip install pyyaml",
        "pip install python-slugify"
    ]
    
    for cmd in flask_requirements:
        run_command(cmd)
    
    # Step 4: Install AI packages (optional)
    print("\n4️⃣ Installing AI packages (optional)...")
    print("   Note: These are only needed for AI captioning feature")
    
    ai_packages = [
        "pip install 'torch>=2.0.0' --index-url https://download.pytorch.org/whl/cpu",
        "pip install 'transformers>=4.30.0,<4.40.0'",
        "pip install 'tensorflow>=2.12.0,<2.16.0'"
    ]
    
    for cmd in ai_packages:
        success = run_command(cmd)
        if not success:
            print(f"   ⚠️  Warning: Could not install AI package. App will work without AI captioning.")
    
    print("\n✅ Dependency fix completed!")
    print("\n📋 Summary:")
    print("   - Flask app dependencies: Installed")
    print("   - AI captioning (optional): Check messages above")
    print("\n🚀 You can now run the app with:")
    print("   python app.py")
    print("   or")
    print("   python run_flask_app.py")

if __name__ == '__main__':
    main()
