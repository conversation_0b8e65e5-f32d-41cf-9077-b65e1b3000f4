<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Toolkit - LoRA Training</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .model-option {
            margin: 10px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        .model-option:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .model-option.selected {
            border-color: #28a745;
            background: #f8fff8;
        }
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 15px;
            padding: 50px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        .upload-area:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .image-card {
            display: inline-block;
            margin: 10px;
            border: 1px solid #ddd;
            border-radius: 12px;
            overflow: hidden;
            width: 250px;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-card img {
            width: 100%;
            height: 180px;
            object-fit: cover;
        }
        .image-card-body {
            padding: 15px;
        }
        .btn-remove {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
        }
        .btn-remove:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="text-center mb-4">
            <h1><i class="fas fa-robot me-3"></i>AI Toolkit - LoRA Training</h1>
            <p class="lead">Professional interface for training custom LoRA models</p>
        </div>

        <!-- Step 1: Model Selection -->
        <div class="card mb-4">
            <div class="card-header">
                <h3><i class="fas fa-cog me-2"></i>Step 1: Choose Model</h3>
            </div>
            <div class="card-body">
                <div id="model-options">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading models...</span>
                        </div>
                        <p class="mt-2">Loading available models...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 2: File Upload -->
        <div class="card mb-4">
            <div class="card-header">
                <h3><i class="fas fa-upload me-2"></i>Step 2: Upload Images</h3>
            </div>
            <div class="card-body">
                <div class="upload-area" id="upload-area">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <h4>Drag & Drop Images Here</h4>
                    <p class="text-muted">or click to browse files</p>
                    <button class="btn btn-primary">
                        <i class="fas fa-folder-open me-2"></i>Browse Files
                    </button>
                    <input type="file" id="file-input" multiple accept="image/*" style="display: none;">
                </div>
                
                <div id="uploaded-images" style="display: none;">
                    <h5 class="mt-4">Uploaded Images (<span id="image-count">0</span>)</h5>
                    <div id="image-grid"></div>
                    <button class="btn btn-outline-danger btn-sm mt-3" onclick="clearAllImages()">
                        <i class="fas fa-trash me-2"></i>Clear All
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 3: Training Controls -->
        <div class="card mb-4" id="training-section" style="display: none;">
            <div class="card-header">
                <h3><i class="fas fa-play me-2"></i>Step 3: Start Training</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">LoRA Name</label>
                        <input type="text" class="form-control" id="lora-name" placeholder="my-custom-lora">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Training Steps</label>
                        <input type="number" class="form-control" id="steps" value="1000" min="100" max="5000">
                    </div>
                </div>
                <div class="mt-4">
                    <button class="btn btn-success btn-lg" onclick="startTraining()">
                        <i class="fas fa-rocket me-2"></i>Start Training
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        console.log('🚀 Simple page loading...');

        // Global variables
        let selectedModel = null;
        let currentSessionId = null;
        let uploadedFiles = [];

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ DOM ready - initializing...');
            loadModels();
            setupFileUpload();
        });

        function loadModels() {
            console.log('📋 Loading models...');
            
            const container = document.getElementById('model-options');
            const models = [
                { key: 'modal_train_lora_flux_24gb', name: 'FLUX.1 Dev (24GB)', desc: 'High-quality generation - RECOMMENDED', recommended: true },
                { key: 'modal_train_lora_flux_schnell_24gb', name: 'FLUX.1 Schnell (24GB)', desc: 'Fast generation' },
                { key: 'train_lora_hidream_48', name: 'HiDream (48GB)', desc: 'High-resolution generation' },
                { key: 'train_lora_wan21_1b_24gb', name: 'WAN 2.1 1B (24GB)', desc: 'Efficient model' },
                { key: 'train_lora_wan21_14b_24gb', name: 'WAN 2.1 14B (24GB)', desc: 'Large model' }
            ];

            let html = '';
            models.forEach(model => {
                html += `
                    <div class="model-option ${model.recommended ? 'selected' : ''}" 
                         onclick="selectModel('${model.key}')" 
                         data-model="${model.key}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h5 class="mb-1">${model.name}</h5>
                                <p class="mb-0 text-muted">${model.desc}</p>
                            </div>
                            ${model.recommended ? '<span class="badge bg-success">⭐ RECOMMENDED</span>' : ''}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
            
            // Auto-select recommended model
            selectedModel = 'modal_train_lora_flux_24gb';
            console.log('✅ Models loaded, auto-selected:', selectedModel);
        }

        function selectModel(modelKey) {
            console.log('🎯 Model selected:', modelKey);
            selectedModel = modelKey;
            
            // Update visual selection
            document.querySelectorAll('.model-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector(`[data-model="${modelKey}"]`).classList.add('selected');
            
            showAlert(`Selected: ${modelKey}`, 'success');
        }

        function setupFileUpload() {
            console.log('📁 Setting up file upload...');
            
            const uploadArea = document.getElementById('upload-area');
            const fileInput = document.getElementById('file-input');

            uploadArea.addEventListener('click', () => fileInput.click());

            fileInput.addEventListener('change', function(e) {
                handleFileUpload(e.target.files);
            });

            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#007bff';
            });

            uploadArea.addEventListener('dragleave', function() {
                uploadArea.style.borderColor = '#ddd';
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#ddd';
                handleFileUpload(e.dataTransfer.files);
            });

            console.log('✅ File upload ready');
        }

        function handleFileUpload(files) {
            console.log('📤 Uploading files:', files.length);
            
            const formData = new FormData();
            Array.from(files).forEach(file => {
                if (file.type.startsWith('image/')) {
                    formData.append('files', file);
                }
            });

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showAlert(data.error, 'danger');
                    return;
                }

                currentSessionId = data.session_id;
                uploadedFiles = data.files;
                displayImages();
                showAlert(`Uploaded ${data.files.length} images`, 'success');
                
                // Show training section
                document.getElementById('training-section').style.display = 'block';
            })
            .catch(error => {
                console.error('Upload error:', error);
                showAlert('Upload failed', 'danger');
            });
        }

        function displayImages() {
            const container = document.getElementById('uploaded-images');
            const grid = document.getElementById('image-grid');
            const count = document.getElementById('image-count');

            if (uploadedFiles.length === 0) {
                container.style.display = 'none';
                return;
            }

            container.style.display = 'block';
            count.textContent = uploadedFiles.length;

            let html = '';
            uploadedFiles.forEach((file, index) => {
                html += `
                    <div class="image-card">
                        <img src="/uploads/${currentSessionId}/${file.filename}" alt="${file.filename}">
                        <div class="image-card-body">
                            <h6>${file.filename}</h6>
                            <p class="text-muted small">${formatFileSize(file.size)}</p>
                            <button class="btn-remove" onclick="removeImage(${index})">
                                <i class="fas fa-times"></i> Remove
                            </button>
                        </div>
                    </div>
                `;
            });

            grid.innerHTML = html;
        }

        function removeImage(index) {
            uploadedFiles.splice(index, 1);
            displayImages();
            if (uploadedFiles.length === 0) {
                document.getElementById('training-section').style.display = 'none';
            }
        }

        function clearAllImages() {
            if (confirm('Remove all images?')) {
                uploadedFiles = [];
                displayImages();
                document.getElementById('training-section').style.display = 'none';
            }
        }

        function startTraining() {
            const loraName = document.getElementById('lora-name').value.trim();
            const steps = document.getElementById('steps').value;

            if (!selectedModel || !currentSessionId || !loraName) {
                showAlert('Please complete all steps first', 'warning');
                return;
            }

            showAlert('Training started! (This is a demo)', 'info');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.querySelector('.main-container').insertBefore(alertDiv, document.querySelector('.main-container').firstChild);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
