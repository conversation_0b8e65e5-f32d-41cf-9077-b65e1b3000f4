{% extends "base.html" %}

{% block title %}AI Toolkit - Advanced LoRA Training{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="section-header">
            <h1><i class="fas fa-robot me-3"></i>Advanced LoRA Training Studio</h1>
            <p class="mb-0">Professional-grade interface for training custom LoRA models with AI-powered captioning</p>
        </div>
    </div>
</div>

<!-- Step 1: Model Selection -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-brain me-2"></i>Step 1: Choose Your Model</h5>
            </div>
            <div class="card-body">
                <div class="model-selector">
                    <div id="model-options">
                        <!-- Model options will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Step 2: File Upload -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-images me-2"></i>Step 2: Upload Your Training Images</h5>
            </div>
            <div class="card-body">
                <div class="upload-area" id="upload-area">
                    <i class="fas fa-cloud-upload-alt fa-4x text-muted mb-4"></i>
                    <h4 class="mb-3">Drag & Drop Images Here</h4>
                    <p class="text-muted mb-4">Or click to browse • Supports JPG, PNG, GIF • Multiple files allowed</p>
                    <input type="file" id="file-input" multiple accept=".png,.jpg,.jpeg,.gif" style="display: none;">
                    <button type="button" class="btn btn-primary-custom btn-custom btn-lg" onclick="document.getElementById('file-input').click()">
                        <i class="fas fa-folder-open me-2"></i>Browse Files
                    </button>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Tip: Upload 10-50 high-quality images for best results
                        </small>
                    </div>
                </div>

                <div id="uploaded-images-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mt-4 mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-images me-2"></i>
                            Uploaded Images (<span id="image-count">0</span>)
                        </h6>
                        <button type="button" class="btn btn-outline-danger btn-sm" id="clear-all-btn">
                            <i class="fas fa-trash me-1"></i>Clear All
                        </button>
                    </div>
                    <div id="image-grid" class="image-grid">
                        <!-- Images will be displayed here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Step 3: AI Captioning -->
<div class="row mb-4" id="captioning-section" style="display: none;">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0"><i class="fas fa-magic me-2"></i>Step 3: AI-Powered Captioning</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-8">
                        <label for="concept-sentence" class="form-label">
                            <i class="fas fa-tag me-2"></i>Trigger Word/Concept (Optional)
                        </label>
                        <input type="text" class="form-control form-control-lg" id="concept-sentence"
                               placeholder="e.g., 'p3rs0n', 'mystyle', 'character_name'">
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            This word will be used to identify your subject in prompts. Leave empty for general training.
                        </small>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <div class="d-grid gap-2 w-100">
                            <button type="button" class="btn btn-success-custom btn-custom btn-lg" id="auto-caption-btn">
                                <i class="fas fa-robot me-2"></i>Generate AI Captions
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-custom" id="manual-caption-btn">
                                <i class="fas fa-edit me-2"></i>Edit Manually
                            </button>
                        </div>
                    </div>
                </div>

                <div id="caption-progress" style="display: none;">
                    <div class="d-flex align-items-center mb-3">
                        <div class="progress progress-custom flex-grow-1 me-3">
                            <div class="progress-bar progress-bar-custom" role="progressbar" style="width: 0%"></div>
                        </div>
                        <span class="text-muted" id="caption-progress-text">0%</span>
                    </div>
                    <p class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        Generating captions with Florence-2 AI model...
                    </p>
                </div>

                <div id="captions-container">
                    <!-- Caption editing interface will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Step 4: Training Configuration -->
<div class="row mb-4" id="training-section" style="display: none;">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Step 4: Training Configuration</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="parameter-group">
                            <h6><i class="fas fa-tag me-2"></i>Model Settings</h6>
                            <div class="mb-4">
                                <label for="lora-name" class="form-label">LoRA Model Name *</label>
                                <input type="text" class="form-control form-control-lg" id="lora-name" required
                                       placeholder="e.g., my-character-v1">
                                <small class="form-text text-muted">Choose a unique name for your trained model</small>
                            </div>
                            <div class="mb-4">
                                <label for="steps" class="form-label">Training Steps</label>
                                <input type="range" class="form-range mb-2" id="steps-range" min="100" max="5000" value="1000" step="50">
                                <input type="number" class="form-control" id="steps" value="1000" min="100" max="5000">
                                <small class="form-text text-muted">More steps = better quality, but longer training time</small>
                            </div>
                            <div class="mb-4">
                                <label for="learning-rate" class="form-label">Learning Rate</label>
                                <select class="form-select" id="learning-rate">
                                    <option value="0.0001">0.0001 (Recommended)</option>
                                    <option value="0.0002">0.0002 (Faster)</option>
                                    <option value="0.00005">0.00005 (Conservative)</option>
                                    <option value="0.0005">0.0005 (Aggressive)</option>
                                </select>
                                <small class="form-text text-muted">Controls how fast the model learns</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="parameter-group">
                            <h6><i class="fas fa-network-wired me-2"></i>Advanced Settings</h6>
                            <div class="mb-4">
                                <label for="rank" class="form-label">LoRA Rank</label>
                                <input type="range" class="form-range mb-2" id="rank-range" min="4" max="128" value="16" step="4">
                                <input type="number" class="form-control" id="rank" value="16" min="4" max="128" step="4">
                                <small class="form-text text-muted">Higher rank = more detail, but larger file size</small>
                            </div>
                            <div class="mb-4">
                                <label for="batch-size" class="form-label">Batch Size</label>
                                <select class="form-select" id="batch-size">
                                    <option value="1" selected>1 (24GB VRAM)</option>
                                    <option value="2">2 (48GB VRAM)</option>
                                    <option value="4">4 (80GB+ VRAM)</option>
                                </select>
                                <small class="form-text text-muted">Higher batch size needs more VRAM</small>
                            </div>
                            <div class="mb-4">
                                <div class="training-summary p-3 bg-light rounded">
                                    <h6><i class="fas fa-info-circle me-2"></i>Training Summary</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <small><strong>Images:</strong> <span id="summary-images">0</span></small><br>
                                            <small><strong>Model:</strong> <span id="summary-model">Not selected</span></small>
                                        </div>
                                        <div class="col-6">
                                            <small><strong>Est. Time:</strong> <span id="summary-time">-</span></small><br>
                                            <small><strong>VRAM Usage:</strong> <span id="summary-vram">~24GB</span></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button type="button" class="btn btn-success-custom btn-custom btn-lg px-5" id="start-training-btn" disabled>
                        <i class="fas fa-rocket me-2"></i>Start Training
                    </button>
                    <button type="button" class="btn btn-danger-custom btn-custom btn-lg px-5" id="stop-training-btn" style="display: none;">
                        <i class="fas fa-stop me-2"></i>Stop Training
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Training Progress -->
<div class="row mb-4" id="progress-section" style="display: none;">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Training Progress</h5>
            </div>
            <div class="card-body">
                <div id="training-progress">
                    <div class="progress progress-custom mb-3">
                        <div id="training-progress-bar" class="progress-bar progress-bar-custom" role="progressbar" style="width: 0%">0%</div>
                    </div>
                    <div id="training-message" class="text-center">
                        <p class="mb-0">Initializing training...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Global variables
let currentSessionId = null;
let selectedModel = null;
let uploadedFiles = [];
let captions = {};

// Predefined models
const AVAILABLE_MODELS = {
    'modal_train_lora_flux_24gb': {
        name: 'FLUX.1 Dev (24GB)',
        description: 'High-quality image generation with FLUX.1 Dev model',
        badges: ['flux', 'high-quality'],
        vram: '24GB',
        recommended: true
    },
    'modal_train_lora_flux_schnell_24gb': {
        name: 'FLUX.1 Schnell (24GB)',
        description: 'Fast image generation with FLUX.1 Schnell model',
        badges: ['flux', 'fast'],
        vram: '24GB',
        recommended: false
    },
    'train_lora_hidream_48': {
        name: 'HiDream (48GB)',
        description: 'High-resolution dream-like image generation',
        badges: ['hidream', 'high-res'],
        vram: '48GB',
        recommended: false
    },
    'train_lora_wan21_1b_24gb': {
        name: 'WAN 2.1 1B (24GB)',
        description: 'Efficient 1B parameter model for quick training',
        badges: ['wan21', 'efficient'],
        vram: '24GB',
        recommended: false
    },
    'train_lora_wan21_14b_24gb': {
        name: 'WAN 2.1 14B (24GB)',
        description: 'Large 14B parameter model for high quality',
        badges: ['wan21', 'large'],
        vram: '24GB',
        recommended: false
    }
};

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the interface
    initializeInterface();

    // Setup event listeners
    setupEventListeners();

    // Load model options
    loadModelOptions();
});

function initializeInterface() {
    // Setup file upload
    setupFileUpload();

    // Setup range sliders
    setupRangeSliders();

    // Update training summary
    updateTrainingSummary();
}

function setupEventListeners() {
    // Captioning buttons
    document.getElementById('auto-caption-btn').addEventListener('click', generateCaptions);
    document.getElementById('manual-caption-btn').addEventListener('click', showManualCaptions);

    // Training buttons
    document.getElementById('start-training-btn').addEventListener('click', startTraining);
    document.getElementById('stop-training-btn').addEventListener('click', stopTraining);

    // Clear all button
    document.getElementById('clear-all-btn').addEventListener('click', clearAllImages);

    // Form inputs for updating summary
    ['lora-name', 'steps', 'rank', 'batch-size'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', updateTrainingSummary);
        }
    });
}

function setupRangeSliders() {
    // Steps range slider
    const stepsRange = document.getElementById('steps-range');
    const stepsInput = document.getElementById('steps');

    stepsRange.addEventListener('input', function() {
        stepsInput.value = this.value;
        updateTrainingSummary();
    });

    stepsInput.addEventListener('input', function() {
        stepsRange.value = this.value;
        updateTrainingSummary();
    });

    // Rank range slider
    const rankRange = document.getElementById('rank-range');
    const rankInput = document.getElementById('rank');

    rankRange.addEventListener('input', function() {
        rankInput.value = this.value;
        updateTrainingSummary();
    });

    rankInput.addEventListener('input', function() {
        rankRange.value = this.value;
        updateTrainingSummary();
    });
}

function loadModelOptions() {
    const modelOptionsContainer = document.getElementById('model-options');
    modelOptionsContainer.innerHTML = '';

    Object.entries(AVAILABLE_MODELS).forEach(([key, model]) => {
        const modelOption = document.createElement('div');
        modelOption.className = 'model-option';
        modelOption.dataset.model = key;

        const badges = model.badges.map(badge =>
            `<span class="model-badge ${badge}">${badge.toUpperCase()}</span>`
        ).join('');

        const recommendedBadge = model.recommended ?
            '<span class="model-badge" style="background: #27ae60;">RECOMMENDED</span>' : '';

        modelOption.innerHTML = `
            <div class="model-name">${model.name}</div>
            <div class="model-description">${model.description}</div>
            <div class="model-badges">
                ${recommendedBadge}
                ${badges}
                <span class="model-badge" style="background: #6c757d;">${model.vram}</span>
            </div>
        `;

        modelOption.addEventListener('click', () => selectModel(key, modelOption));
        modelOptionsContainer.appendChild(modelOption);
    });

    // Auto-select recommended model
    const recommendedModel = Object.entries(AVAILABLE_MODELS).find(([key, model]) => model.recommended);
    if (recommendedModel) {
        const [key] = recommendedModel;
        const modelElement = document.querySelector(`[data-model="${key}"]`);
        selectModel(key, modelElement);
    }
}

function selectModel(modelKey, modelElement) {
    // Remove previous selection
    document.querySelectorAll('.model-option').forEach(option => {
        option.classList.remove('selected');
    });

    // Select current model
    modelElement.classList.add('selected');
    selectedModel = modelKey;

    // Update training summary
    updateTrainingSummary();

    // Show next step if we have images
    if (uploadedFiles.length > 0) {
        document.getElementById('captioning-section').style.display = 'block';
    }
}

function setupFileUpload() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        handleFileUpload(files);
    });

    // File input handler
    fileInput.addEventListener('change', (e) => {
        handleFileUpload(e.target.files);
    });
}

function handleFileUpload(files) {
    if (files.length === 0) return;

    const formData = new FormData();
    Array.from(files).forEach(file => {
        // Only add image files
        if (file.type.startsWith('image/')) {
            formData.append('files', file);
        }
    });

    // Show upload progress
    showAlert('Uploading images...', 'info');

    fetch('/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showAlert(data.error, 'danger');
            return;
        }

        currentSessionId = data.session_id;

        // Add new files to existing ones
        data.files.forEach(file => {
            if (file.type === 'image') {
                uploadedFiles.push(file);
            }
        });

        updateImageGrid();
        showAlert(`Successfully uploaded ${data.files.length} images`, 'success');

        // Show next sections
        if (selectedModel) {
            document.getElementById('captioning-section').style.display = 'block';
        }

        updateTrainingSummary();
    })
    .catch(error => {
        console.error('Upload error:', error);
        showAlert('Upload failed', 'danger');
    });
}

function updateImageGrid() {
    const imageGrid = document.getElementById('image-grid');
    const uploadedSection = document.getElementById('uploaded-images-section');
    const imageCount = document.getElementById('image-count');

    if (uploadedFiles.length === 0) {
        uploadedSection.style.display = 'none';
        return;
    }

    uploadedSection.style.display = 'block';
    imageCount.textContent = uploadedFiles.length;

    imageGrid.innerHTML = '';

    uploadedFiles.forEach((file, index) => {
        const imageCard = document.createElement('div');
        imageCard.className = 'image-card';
        imageCard.dataset.index = index;

        imageCard.innerHTML = `
            <img src="/uploads/${currentSessionId}/${file.filename}" alt="${file.filename}">
            <button class="remove-image-btn" onclick="removeImage(${index})">
                <i class="fas fa-times"></i>
            </button>
            <div class="image-card-body">
                <div class="image-card-title">${file.filename}</div>
                <div class="image-card-info">
                    ${formatFileSize(file.size)}
                    ${file.width && file.height ? ` • ${file.width}×${file.height}` : ''}
                </div>
            </div>
        `;

        imageGrid.appendChild(imageCard);
    });
}

function removeImage(index) {
    uploadedFiles.splice(index, 1);
    updateImageGrid();
    updateTrainingSummary();

    // Hide sections if no images
    if (uploadedFiles.length === 0) {
        document.getElementById('captioning-section').style.display = 'none';
        document.getElementById('training-section').style.display = 'none';
    }
}

function clearAllImages() {
    if (confirm('Are you sure you want to remove all images?')) {
        uploadedFiles = [];
        captions = {};
        updateImageGrid();
        updateTrainingSummary();

        // Hide sections
        document.getElementById('captioning-section').style.display = 'none';
        document.getElementById('training-section').style.display = 'none';
    }
}

function updateTrainingSummary() {
    const summaryImages = document.getElementById('summary-images');
    const summaryModel = document.getElementById('summary-model');
    const summaryTime = document.getElementById('summary-time');
    const summaryVram = document.getElementById('summary-vram');
    const startBtn = document.getElementById('start-training-btn');
    const loraName = document.getElementById('lora-name').value;
    const steps = parseInt(document.getElementById('steps').value);

    // Update summary
    summaryImages.textContent = uploadedFiles.length;
    summaryModel.textContent = selectedModel ? AVAILABLE_MODELS[selectedModel].name : 'Not selected';
    summaryVram.textContent = selectedModel ? `~${AVAILABLE_MODELS[selectedModel].vram}` : '~24GB';

    // Estimate training time (rough calculation)
    if (uploadedFiles.length > 0 && steps) {
        const timePerStep = 2; // seconds per step (rough estimate)
        const totalMinutes = Math.round((steps * timePerStep) / 60);
        if (totalMinutes < 60) {
            summaryTime.textContent = `~${totalMinutes} min`;
        } else {
            const hours = Math.floor(totalMinutes / 60);
            const minutes = totalMinutes % 60;
            summaryTime.textContent = `~${hours}h ${minutes}m`;
        }
    } else {
        summaryTime.textContent = '-';
    }

    // Enable/disable start button
    const canStart = selectedModel && uploadedFiles.length > 0 && loraName.trim();
    startBtn.disabled = !canStart;
}

function generateCaptions() {
    if (!currentSessionId || uploadedFiles.length === 0) {
        showAlert('Please upload images first', 'warning');
        return;
    }

    const conceptSentence = document.getElementById('concept-sentence').value;
    const progressDiv = document.getElementById('caption-progress');
    const progressBar = progressDiv.querySelector('.progress-bar');
    const progressText = document.getElementById('caption-progress-text');

    progressDiv.style.display = 'block';
    progressBar.style.width = '0%';
    progressText.textContent = '0%';

    document.getElementById('auto-caption-btn').disabled = true;

    fetch('/api/caption', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            session_id: currentSessionId,
            concept_sentence: conceptSentence
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showAlert(data.error, 'danger');
            return;
        }

        captions = data.captions;

        progressBar.style.width = '100%';
        progressText.textContent = '100%';
        showAlert(data.message, 'success');

        setTimeout(() => {
            progressDiv.style.display = 'none';
            showCaptionEditor();
        }, 2000);
    })
    .catch(error => {
        console.error('Captioning error:', error);
        showAlert('Captioning failed', 'danger');
    })
    .finally(() => {
        document.getElementById('auto-caption-btn').disabled = false;
    });
}

function showCaptionEditor() {
    const captionsContainer = document.getElementById('captions-container');
    captionsContainer.innerHTML = '';

    if (uploadedFiles.length === 0) return;

    const editorHTML = `
        <div class="caption-editor">
            <h6><i class="fas fa-edit me-2"></i>Edit Captions</h6>
            <p class="text-muted mb-4">Review and edit the AI-generated captions for your images.</p>
            <div class="row" id="caption-cards">
                ${uploadedFiles.map((file, index) => `
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="row g-0">
                                <div class="col-4">
                                    <img src="/uploads/${currentSessionId}/${file.filename}"
                                         class="img-fluid rounded-start h-100"
                                         style="object-fit: cover;" alt="${file.filename}">
                                </div>
                                <div class="col-8">
                                    <div class="card-body">
                                        <h6 class="card-title">${file.filename}</h6>
                                        <textarea class="form-control caption-input"
                                                  data-filename="${file.filename}"
                                                  placeholder="Enter caption for this image..."
                                                  rows="3">${captions[file.filename] || ''}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;

    captionsContainer.innerHTML = editorHTML;

    // Add event listeners for caption inputs
    document.querySelectorAll('.caption-input').forEach(input => {
        input.addEventListener('input', (e) => {
            const filename = e.target.dataset.filename;
            captions[filename] = e.target.value;
        });
    });

    // Show training section
    document.getElementById('training-section').style.display = 'block';
}

function showManualCaptions() {
    if (!currentSessionId || uploadedFiles.length === 0) {
        showAlert('Please upload images first', 'warning');
        return;
    }

    // Initialize empty captions if not already set
    uploadedFiles.forEach(file => {
        if (!captions[file.filename]) {
            captions[file.filename] = '';
        }
    });

    showCaptionEditor();
    showAlert('You can now manually edit captions for each image', 'info');
}

function startTraining() {
    if (!currentSessionId || !selectedModel || uploadedFiles.length === 0) {
        showAlert('Please complete all steps before starting training', 'warning');
        return;
    }

    const loraName = document.getElementById('lora-name').value.trim();
    if (!loraName) {
        showAlert('Please enter a LoRA name', 'warning');
        return;
    }

    const trainingData = {
        session_id: currentSessionId,
        config_name: selectedModel,
        lora_name: loraName,
        concept_sentence: document.getElementById('concept-sentence').value,
        captions: captions,
        steps: parseInt(document.getElementById('steps').value),
        learning_rate: parseFloat(document.getElementById('learning-rate').value),
        rank: parseInt(document.getElementById('rank').value),
        batch_size: parseInt(document.getElementById('batch-size').value)
    };

    document.getElementById('start-training-btn').style.display = 'none';
    document.getElementById('stop-training-btn').style.display = 'inline-block';
    document.getElementById('progress-section').style.display = 'block';

    fetch('/api/train', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(trainingData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showAlert(data.error, 'danger');
            resetTrainingUI();
            return;
        }

        showAlert(data.message, 'success');
        startProgressPolling();
    })
    .catch(error => {
        console.error('Training error:', error);
        showAlert('Failed to start training', 'danger');
        resetTrainingUI();
    });
}

function stopTraining() {
    fetch('/api/training/stop', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        showAlert(data.message, 'info');
        resetTrainingUI();
    })
    .catch(error => {
        console.error('Stop training error:', error);
        showAlert('Failed to stop training', 'danger');
    });
}

function resetTrainingUI() {
    document.getElementById('start-training-btn').style.display = 'inline-block';
    document.getElementById('stop-training-btn').style.display = 'none';
    document.getElementById('progress-section').style.display = 'none';
}

function startProgressPolling() {
    const progressBar = document.getElementById('training-progress-bar');
    const messageDiv = document.getElementById('training-message');

    const pollInterval = setInterval(() => {
        fetch('/api/training/status')
            .then(response => response.json())
            .then(data => {
                if (!data.is_training) {
                    clearInterval(pollInterval);
                    resetTrainingUI();

                    if (data.error) {
                        showAlert('Training failed: ' + data.error, 'danger');
                    } else if (data.progress === 100) {
                        showAlert('Training completed successfully!', 'success');
                    }
                } else {
                    progressBar.style.width = data.progress + '%';
                    progressBar.textContent = data.progress + '%';
                    messageDiv.innerHTML = `<p class="mb-0">${data.message}</p>`;
                }
            })
            .catch(error => {
                console.error('Progress polling error:', error);
            });
    }, 2000);
}
</script>
{% endblock %}

    imageGrid.innerHTML = '';

    uploadedFiles.forEach((file, index) => {
        const imageCard = document.createElement('div');
        imageCard.className = 'image-card';
        imageCard.dataset.index = index;

        imageCard.innerHTML = `
            <img src="/uploads/${currentSessionId}/${file.filename}" alt="${file.filename}">
            <button class="remove-image-btn" onclick="removeImage(${index})">
                <i class="fas fa-times"></i>
            </button>
            <div class="image-card-body">
                <div class="image-card-title">${file.filename}</div>
                <div class="image-card-info">
                    ${formatFileSize(file.size)}
                    ${file.width && file.height ? ` • ${file.width}×${file.height}` : ''}
                </div>
            </div>
        `;

        imageGrid.appendChild(imageCard);
    });
}

function removeImage(index) {
    uploadedFiles.splice(index, 1);
    updateImageGrid();
    updateTrainingSummary();

    // Hide sections if no images
    if (uploadedFiles.length === 0) {
        document.getElementById('captioning-section').style.display = 'none';
        document.getElementById('training-section').style.display = 'none';
    }
}

function clearAllImages() {
    if (confirm('Are you sure you want to remove all images?')) {
        uploadedFiles = [];
        captions = {};
        updateImageGrid();
        updateTrainingSummary();

        // Hide sections
        document.getElementById('captioning-section').style.display = 'none';
        document.getElementById('training-section').style.display = 'none';
    }
}

function updateTrainingSummary() {
    const summaryImages = document.getElementById('summary-images');
    const summaryModel = document.getElementById('summary-model');
    const summaryTime = document.getElementById('summary-time');
    const summaryVram = document.getElementById('summary-vram');
    const startBtn = document.getElementById('start-training-btn');
    const loraName = document.getElementById('lora-name').value;
    const steps = parseInt(document.getElementById('steps').value);

    // Update summary
    summaryImages.textContent = uploadedFiles.length;
    summaryModel.textContent = selectedModel ? AVAILABLE_MODELS[selectedModel].name : 'Not selected';
    summaryVram.textContent = selectedModel ? `~${AVAILABLE_MODELS[selectedModel].vram}` : '~24GB';

    // Estimate training time (rough calculation)
    if (uploadedFiles.length > 0 && steps) {
        const timePerStep = 2; // seconds per step (rough estimate)
        const totalMinutes = Math.round((steps * timePerStep) / 60);
        if (totalMinutes < 60) {
            summaryTime.textContent = `~${totalMinutes} min`;
        } else {
            const hours = Math.floor(totalMinutes / 60);
            const minutes = totalMinutes % 60;
            summaryTime.textContent = `~${hours}h ${minutes}m`;
        }
    } else {
        summaryTime.textContent = '-';
    }

    // Enable/disable start button
    const canStart = selectedModel && uploadedFiles.length > 0 && loraName.trim();
    startBtn.disabled = !canStart;
}

function generateCaptions() {
    if (!currentSessionId || uploadedFiles.length === 0) {
        showAlert('Please upload images first', 'warning');
        return;
    }

    const conceptSentence = document.getElementById('concept-sentence').value;
    const progressDiv = document.getElementById('caption-progress');
    const progressBar = progressDiv.querySelector('.progress-bar');
    const progressText = document.getElementById('caption-progress-text');

    progressDiv.style.display = 'block';
    progressBar.style.width = '0%';
    progressText.textContent = '0%';

    document.getElementById('auto-caption-btn').disabled = true;

    fetch('/api/caption', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            session_id: currentSessionId,
            concept_sentence: conceptSentence
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showAlert(data.error, 'danger');
            return;
        }

        captions = data.captions;

        progressBar.style.width = '100%';
        progressText.textContent = '100%';
        showAlert(data.message, 'success');

        setTimeout(() => {
            progressDiv.style.display = 'none';
            showCaptionEditor();
        }, 2000);
    })
    .catch(error => {
        console.error('Captioning error:', error);
        showAlert('Captioning failed', 'danger');
    })
    .finally(() => {
        document.getElementById('auto-caption-btn').disabled = false;
    });
}

function showCaptionEditor() {
    const captionsContainer = document.getElementById('captions-container');
    captionsContainer.innerHTML = '';

    if (uploadedFiles.length === 0) return;

    const editorHTML = `
        <div class="caption-editor">
            <h6><i class="fas fa-edit me-2"></i>Edit Captions</h6>
            <p class="text-muted mb-4">Review and edit the AI-generated captions for your images.</p>
            <div class="row" id="caption-cards">
                ${uploadedFiles.map((file, index) => `
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="row g-0">
                                <div class="col-4">
                                    <img src="/uploads/${currentSessionId}/${file.filename}"
                                         class="img-fluid rounded-start h-100"
                                         style="object-fit: cover;" alt="${file.filename}">
                                </div>
                                <div class="col-8">
                                    <div class="card-body">
                                        <h6 class="card-title">${file.filename}</h6>
                                        <textarea class="form-control caption-input"
                                                  data-filename="${file.filename}"
                                                  placeholder="Enter caption for this image..."
                                                  rows="3">${captions[file.filename] || ''}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;

    captionsContainer.innerHTML = editorHTML;

    // Add event listeners for caption inputs
    document.querySelectorAll('.caption-input').forEach(input => {
        input.addEventListener('input', (e) => {
            const filename = e.target.dataset.filename;
            captions[filename] = e.target.value;
        });
    });

    // Show training section
    document.getElementById('training-section').style.display = 'block';
}

function showManualCaptions() {
    if (!currentSessionId || uploadedFiles.length === 0) {
        showAlert('Please upload images first', 'warning');
        return;
    }

    // Initialize empty captions if not already set
    uploadedFiles.forEach(file => {
        if (!captions[file.filename]) {
            captions[file.filename] = '';
        }
    });

    showCaptionEditor();
    showAlert('You can now manually edit captions for each image', 'info');
}

function startTraining() {
    if (!currentSessionId || !selectedModel || uploadedFiles.length === 0) {
        showAlert('Please complete all steps before starting training', 'warning');
        return;
    }

    const loraName = document.getElementById('lora-name').value.trim();
    if (!loraName) {
        showAlert('Please enter a LoRA name', 'warning');
        return;
    }

    const trainingData = {
        session_id: currentSessionId,
        config_name: selectedModel,
        lora_name: loraName,
        concept_sentence: document.getElementById('concept-sentence').value,
        captions: captions,
        steps: parseInt(document.getElementById('steps').value),
        learning_rate: parseFloat(document.getElementById('learning-rate').value),
        rank: parseInt(document.getElementById('rank').value),
        batch_size: parseInt(document.getElementById('batch-size').value)
    };

    document.getElementById('start-training-btn').style.display = 'none';
    document.getElementById('stop-training-btn').style.display = 'inline-block';
    document.getElementById('progress-section').style.display = 'block';

    fetch('/api/train', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(trainingData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showAlert(data.error, 'danger');
            resetTrainingUI();
            return;
        }

        showAlert(data.message, 'success');
        startProgressPolling();
    })
    .catch(error => {
        console.error('Training error:', error);
        showAlert('Failed to start training', 'danger');
        resetTrainingUI();
    });
}

function stopTraining() {
    fetch('/api/training/stop', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        showAlert(data.message, 'info');
        resetTrainingUI();
    })
    .catch(error => {
        console.error('Stop training error:', error);
        showAlert('Failed to stop training', 'danger');
    });
}

function resetTrainingUI() {
    document.getElementById('start-training-btn').style.display = 'inline-block';
    document.getElementById('stop-training-btn').style.display = 'none';
    document.getElementById('progress-section').style.display = 'none';
}

function startProgressPolling() {
    const progressBar = document.getElementById('training-progress-bar');
    const messageDiv = document.getElementById('training-message');

    const pollInterval = setInterval(() => {
        fetch('/api/training/status')
            .then(response => response.json())
            .then(data => {
                if (!data.is_training) {
                    clearInterval(pollInterval);
                    resetTrainingUI();

                    if (data.error) {
                        showAlert('Training failed: ' + data.error, 'danger');
                    } else if (data.progress === 100) {
                        showAlert('Training completed successfully!', 'success');
                    }
                } else {
                    progressBar.style.width = data.progress + '%';
                    progressBar.textContent = data.progress + '%';
                    messageDiv.innerHTML = `<p class="mb-0">${data.message}</p>`;
                }
            })
            .catch(error => {
                console.error('Progress polling error:', error);
            });
    }, 2000);
}
</script>
{% endblock %}
