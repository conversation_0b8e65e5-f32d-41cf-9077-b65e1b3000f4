{% extends "base.html" %}

{% block title %}AI Toolkit - Advanced LoRA Training{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="section-header">
            <h1><i class="fas fa-robot me-3"></i>Advanced LoRA Training Studio</h1>
            <p class="mb-0">Professional-grade interface for training custom LoRA models with AI-powered captioning</p>
        </div>
    </div>
</div>

<!-- Step 1: Model Selection -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-brain me-2"></i>Step 1: Choose Your Model</h5>
            </div>
            <div class="card-body">
                <div class="model-selector">
                    <div id="model-options">
                        <p style="color: red; font-weight: bold;" id="loading-message">Model options container - JavaScript should populate this</p>
                        <!-- Model options will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Step 2: File Upload -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-images me-2"></i>Step 2: Upload Your Training Images</h5>
            </div>
            <div class="card-body">
                <div class="upload-area" id="upload-area">
                    <i class="fas fa-cloud-upload-alt fa-4x text-muted mb-4"></i>
                    <h4 class="mb-3">Drag & Drop Images Here</h4>
                    <p class="text-muted mb-4">Or click to browse • Supports JPG, PNG, GIF • Multiple files allowed</p>
                    <input type="file" id="file-input" multiple accept=".png,.jpg,.jpeg,.gif" style="display: none;">
                    <button type="button" class="btn btn-primary-custom btn-custom btn-lg" onclick="document.getElementById('file-input').click()">
                        <i class="fas fa-folder-open me-2"></i>Browse Files
                    </button>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Tip: Upload 10-50 high-quality images for best results
                        </small>
                    </div>
                </div>

                <div id="uploaded-images-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mt-4 mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-images me-2"></i>
                            Uploaded Images (<span id="image-count">0</span>)
                        </h6>
                        <button type="button" class="btn btn-outline-danger btn-sm" id="clear-all-btn">
                            <i class="fas fa-trash me-1"></i>Clear All
                        </button>
                    </div>
                    <div id="image-grid" class="image-grid">
                        <!-- Images will be displayed here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Step 3: AI Captioning -->
<div class="row mb-4" id="captioning-section" style="display: none;">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0"><i class="fas fa-magic me-2"></i>Step 3: AI-Powered Captioning</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-8">
                        <label for="concept-sentence" class="form-label">
                            <i class="fas fa-tag me-2"></i>Trigger Word/Concept (Optional)
                        </label>
                        <input type="text" class="form-control form-control-lg" id="concept-sentence"
                               placeholder="e.g., 'p3rs0n', 'mystyle', 'character_name'">
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            This word will be used to identify your subject in prompts. Leave empty for general training.
                        </small>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <div class="d-grid gap-2 w-100">
                            <button type="button" class="btn btn-success-custom btn-custom btn-lg" id="auto-caption-btn">
                                <i class="fas fa-robot me-2"></i>Generate AI Captions
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-custom" id="manual-caption-btn">
                                <i class="fas fa-edit me-2"></i>Edit Manually
                            </button>
                        </div>
                    </div>
                </div>

                <div id="caption-progress" style="display: none;">
                    <div class="d-flex align-items-center mb-3">
                        <div class="progress progress-custom flex-grow-1 me-3">
                            <div class="progress-bar progress-bar-custom" role="progressbar" style="width: 0%"></div>
                        </div>
                        <span class="text-muted" id="caption-progress-text">0%</span>
                    </div>
                    <p class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        Generating captions with Florence-2 AI model...
                    </p>
                </div>

                <div id="captions-container">
                    <!-- Caption editing interface will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Step 4: Training Configuration -->
<div class="row mb-4" id="training-section" style="display: none;">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Step 4: Training Configuration</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="parameter-group">
                            <h6><i class="fas fa-tag me-2"></i>Model Settings</h6>
                            <div class="mb-4">
                                <label for="lora-name" class="form-label">LoRA Model Name *</label>
                                <input type="text" class="form-control form-control-lg" id="lora-name" required
                                       placeholder="e.g., my-character-v1">
                                <small class="form-text text-muted">Choose a unique name for your trained model</small>
                            </div>
                            <div class="mb-4">
                                <label for="steps" class="form-label">Training Steps</label>
                                <input type="range" class="form-range mb-2" id="steps-range" min="100" max="5000" value="1000" step="50">
                                <input type="number" class="form-control" id="steps" value="1000" min="100" max="5000">
                                <small class="form-text text-muted">More steps = better quality, but longer training time</small>
                            </div>
                            <div class="mb-4">
                                <label for="learning-rate" class="form-label">Learning Rate</label>
                                <select class="form-select" id="learning-rate">
                                    <option value="0.0001">0.0001 (Recommended)</option>
                                    <option value="0.0002">0.0002 (Faster)</option>
                                    <option value="0.00005">0.00005 (Conservative)</option>
                                    <option value="0.0005">0.0005 (Aggressive)</option>
                                </select>
                                <small class="form-text text-muted">Controls how fast the model learns</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="parameter-group">
                            <h6><i class="fas fa-network-wired me-2"></i>Advanced Settings</h6>
                            <div class="mb-4">
                                <label for="rank" class="form-label">LoRA Rank</label>
                                <input type="range" class="form-range mb-2" id="rank-range" min="4" max="128" value="16" step="4">
                                <input type="number" class="form-control" id="rank" value="16" min="4" max="128" step="4">
                                <small class="form-text text-muted">Higher rank = more detail, but larger file size</small>
                            </div>
                            <div class="mb-4">
                                <label for="batch-size" class="form-label">Batch Size</label>
                                <select class="form-select" id="batch-size">
                                    <option value="1" selected>1 (24GB VRAM)</option>
                                    <option value="2">2 (48GB VRAM)</option>
                                    <option value="4">4 (80GB+ VRAM)</option>
                                </select>
                                <small class="form-text text-muted">Higher batch size needs more VRAM</small>
                            </div>
                            <div class="mb-4">
                                <div class="training-summary p-3 bg-light rounded">
                                    <h6><i class="fas fa-info-circle me-2"></i>Training Summary</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <small><strong>Images:</strong> <span id="summary-images">0</span></small><br>
                                            <small><strong>Model:</strong> <span id="summary-model">Not selected</span></small>
                                        </div>
                                        <div class="col-6">
                                            <small><strong>Est. Time:</strong> <span id="summary-time">-</span></small><br>
                                            <small><strong>VRAM Usage:</strong> <span id="summary-vram">~24GB</span></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button type="button" class="btn btn-success-custom btn-custom btn-lg px-5" id="start-training-btn" disabled>
                        <i class="fas fa-rocket me-2"></i>Start Training
                    </button>
                    <button type="button" class="btn btn-danger-custom btn-custom btn-lg px-5" id="stop-training-btn" style="display: none;">
                        <i class="fas fa-stop me-2"></i>Stop Training
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Training Progress -->
<div class="row mb-4" id="progress-section" style="display: none;">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Training Progress</h5>
            </div>
            <div class="card-body">
                <div id="training-progress">
                    <div class="progress progress-custom mb-3">
                        <div id="training-progress-bar" class="progress-bar progress-bar-custom" role="progressbar" style="width: 0%">0%</div>
                    </div>
                    <div id="training-message" class="text-center">
                        <p class="mb-0">Initializing training...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
console.log('🚀 JavaScript loading...');

// Immediate test - this should run right away
try {
    const testElement = document.getElementById('loading-message');
    if (testElement) {
        testElement.innerHTML = '✅ JavaScript is running! Loading models...';
        testElement.style.color = 'green';
    }
} catch (error) {
    console.error('Immediate test failed:', error);
}

// Use global variables from base template (avoid conflicts)
let selectedModel = 'modal_train_lora_flux_24gb'; // Default selection

// Simple test
document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ DOM loaded, starting simple test...');

    // Test if we can find the model options container
    const container = document.getElementById('model-options');
    if (container) {
        console.log('✅ Found model options container');

        // Clear the test message and add models
        container.innerHTML = '<h3 style="color: green;">JavaScript is working! Loading models...</h3>';

        // Simple model data
        const models = [
            {
                key: 'modal_train_lora_flux_24gb',
                name: 'FLUX.1 Dev (24GB)',
                description: 'High-quality image generation - RECOMMENDED',
                vram: '24GB'
            },
            {
                key: 'modal_train_lora_flux_schnell_24gb',
                name: 'FLUX.1 Schnell (24GB)',
                description: 'Fast image generation',
                vram: '24GB'
            },
            {
                key: 'train_lora_hidream_48',
                name: 'HiDream (48GB)',
                description: 'High-resolution dream-like generation',
                vram: '48GB'
            },
            {
                key: 'train_lora_wan21_1b_24gb',
                name: 'WAN 2.1 1B (24GB)',
                description: 'Efficient 1B parameter model',
                vram: '24GB'
            },
            {
                key: 'train_lora_wan21_14b_24gb',
                name: 'WAN 2.1 14B (24GB)',
                description: 'Large 14B parameter model',
                vram: '24GB'
            }
        ];

        // Create model options
        let html = '';
        models.forEach(model => {
            const isRecommended = model.key === 'modal_train_lora_flux_24gb';
            html += `
                <div class="model-option ${isRecommended ? 'selected' : ''}"
                     onclick="selectModel('${model.key}')"
                     data-model="${model.key}"
                     style="margin-bottom: 15px; padding: 15px; border: 2px solid ${isRecommended ? '#28a745' : '#ddd'}; border-radius: 8px; cursor: pointer; background: ${isRecommended ? '#f8fff8' : 'white'};">
                    <div style="font-weight: bold; font-size: 18px; color: #333;">${model.name}</div>
                    <div style="color: #666; margin: 5px 0;">${model.description}</div>
                    <div style="font-size: 12px; color: #999;">VRAM: ${model.vram}</div>
                    ${isRecommended ? '<div style="color: #28a745; font-weight: bold; margin-top: 5px;">⭐ RECOMMENDED</div>' : ''}
                </div>
            `;
        });

        container.innerHTML = html;
        console.log('✅ Models loaded successfully');

        // Set up file upload
        setupFileUpload();

    } else {
        console.error('❌ Model options container not found!');
    }
});

function selectModel(modelKey) {
    console.log('🎯 Model selected:', modelKey);
    selectedModel = modelKey;

    // Update visual selection
    document.querySelectorAll('.model-option').forEach(option => {
        option.style.border = '2px solid #ddd';
        option.style.background = 'white';
        option.classList.remove('selected');
    });

    const selectedOption = document.querySelector(`[data-model="${modelKey}"]`);
    if (selectedOption) {
        selectedOption.style.border = '2px solid #28a745';
        selectedOption.style.background = '#f8fff8';
        selectedOption.classList.add('selected');
    }

    // Show success message
    if (window.showAlert) {
        showAlert(`Selected model: ${modelKey}`, 'success');
    }
}

function setupFileUpload() {
    console.log('🔧 Setting up file upload...');

    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');

    if (!uploadArea || !fileInput) {
        console.error('❌ Upload elements not found');
        return;
    }

    // File input change handler
    fileInput.addEventListener('change', function(e) {
        console.log('📁 Files selected:', e.target.files.length);
        handleFileUpload(e.target.files);
    });

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.style.background = '#e3f2fd';
    });

    uploadArea.addEventListener('dragleave', function(e) {
        uploadArea.style.background = '';
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.style.background = '';
        console.log('📁 Files dropped:', e.dataTransfer.files.length);
        handleFileUpload(e.dataTransfer.files);
    });

    console.log('✅ File upload setup complete');
}

function handleFileUpload(files) {
    console.log('📤 Uploading files:', files.length);

    if (files.length === 0) return;

    const formData = new FormData();
    let imageCount = 0;

    Array.from(files).forEach(file => {
        if (file.type.startsWith('image/')) {
            formData.append('files', file);
            imageCount++;
        }
    });

    if (imageCount === 0) {
        alert('Please select image files (JPG, PNG, GIF)');
        return;
    }

    // Show upload message
    if (window.showAlert) {
        showAlert(`Uploading ${imageCount} images...`, 'info');
    }

    fetch('/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        console.log('📤 Upload response:', data);

        if (data.error) {
            alert('Upload error: ' + data.error);
            return;
        }

        currentSessionId = data.session_id;
        uploadedFiles = data.files;

        // Show uploaded images
        displayUploadedImages();

        if (window.showAlert) {
            showAlert(`Successfully uploaded ${data.files.length} images`, 'success');
        }

        // Show next section
        document.getElementById('captioning-section').style.display = 'block';
    })
    .catch(error => {
        console.error('📤 Upload error:', error);
        alert('Upload failed: ' + error.message);
    });
}

function displayUploadedImages() {
    console.log('🖼️ Displaying uploaded images:', uploadedFiles.length);

    const imageGrid = document.getElementById('image-grid');
    const uploadedSection = document.getElementById('uploaded-images-section');
    const imageCount = document.getElementById('image-count');

    if (!imageGrid || !uploadedSection || !imageCount) {
        console.error('❌ Image display elements not found');
        return;
    }

    if (uploadedFiles.length === 0) {
        uploadedSection.style.display = 'none';
        return;
    }

    uploadedSection.style.display = 'block';
    imageCount.textContent = uploadedFiles.length;

    let html = '';
    uploadedFiles.forEach((file, index) => {
        html += `
            <div class="image-card" style="display: inline-block; margin: 10px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; width: 200px;">
                <img src="/uploads/${currentSessionId}/${file.filename}"
                     alt="${file.filename}"
                     style="width: 100%; height: 150px; object-fit: cover;">
                <div style="padding: 10px;">
                    <div style="font-weight: bold; font-size: 14px; margin-bottom: 5px;">${file.filename}</div>
                    <div style="font-size: 12px; color: #666;">
                        ${formatFileSize(file.size)}
                        ${file.width && file.height ? ` • ${file.width}×${file.height}` : ''}
                    </div>
                    <button onclick="removeImage(${index})"
                            style="margin-top: 5px; padding: 5px 10px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        Remove
                    </button>
                </div>
            </div>
        `;
    });

    imageGrid.innerHTML = html;
    console.log('✅ Images displayed successfully');
}

function removeImage(index) {
    console.log('🗑️ Removing image at index:', index);
    uploadedFiles.splice(index, 1);
    displayUploadedImages();

    if (uploadedFiles.length === 0) {
        document.getElementById('captioning-section').style.display = 'none';
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>
{% endblock %}
