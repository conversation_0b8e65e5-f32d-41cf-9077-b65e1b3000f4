{% extends "base.html" %}

{% block title %}AI Toolkit - LoRA Training Interface{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="section-header">
            <h2><i class="fas fa-magic me-2"></i>LoRA Training Interface</h2>
            <p class="mb-0">Upload images/videos, configure parameters, and train custom LoRA models</p>
        </div>
    </div>
</div>

<!-- Step 1: File Upload -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-upload me-2"></i>Step 1: Upload Images/Videos</h5>
            </div>
            <div class="card-body">
                <div class="upload-area" id="upload-area">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <h5>Drag & Drop files here or click to browse</h5>
                    <p class="text-muted">Supported formats: PNG, JPG, JPEG, GIF, MP4, AVI, MOV, TXT</p>
                    <input type="file" id="file-input" multiple accept=".png,.jpg,.jpeg,.gif,.mp4,.avi,.mov,.txt" style="display: none;">
                    <button type="button" class="btn btn-primary-custom btn-custom" onclick="document.getElementById('file-input').click()">
                        <i class="fas fa-folder-open me-2"></i>Browse Files
                    </button>
                </div>
                
                <div id="file-list" class="mt-4" style="display: none;">
                    <h6>Uploaded Files:</h6>
                    <div id="files-container" class="row"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Step 2: Model Configuration -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Step 2: Select Model Configuration</h5>
            </div>
            <div class="card-body">
                <div id="config-list" class="row">
                    <!-- Configs will be loaded here -->
                </div>
                
                <div id="config-details" class="mt-4" style="display: none;">
                    <div class="model-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Model Information</h6>
                        <div id="model-info-content"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Step 3: Captioning -->
<div class="row mb-4" id="captioning-section" style="display: none;">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0"><i class="fas fa-comment-alt me-2"></i>Step 3: Image Captioning</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="concept-sentence" class="form-label">Trigger Word/Concept (Optional)</label>
                        <input type="text" class="form-control" id="concept-sentence" 
                               placeholder="e.g., 'p3rs0n', 'in the style of CNSTLL'">
                        <small class="form-text text-muted">This will be added as [trigger] in captions</small>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="button" class="btn btn-primary-custom btn-custom me-2" id="auto-caption-btn">
                            <i class="fas fa-robot me-2"></i>Generate AI Captions
                        </button>
                        <button type="button" class="btn btn-secondary btn-custom" id="manual-caption-btn">
                            <i class="fas fa-edit me-2"></i>Manual Captions
                        </button>
                    </div>
                </div>
                
                <div id="caption-progress" style="display: none;">
                    <div class="progress progress-custom mb-3">
                        <div class="progress-bar progress-bar-custom" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                
                <div id="captions-container" class="row">
                    <!-- Captions will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Step 4: Training Parameters -->
<div class="row mb-4" id="training-section" style="display: none;">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-sliders-h me-2"></i>Step 4: Training Parameters</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="parameter-group">
                            <h6><i class="fas fa-tag me-2"></i>Basic Settings</h6>
                            <div class="mb-3">
                                <label for="lora-name" class="form-label">LoRA Name *</label>
                                <input type="text" class="form-control" id="lora-name" required
                                       placeholder="e.g., my-awesome-lora">
                            </div>
                            <div class="mb-3">
                                <label for="steps" class="form-label">Training Steps</label>
                                <input type="number" class="form-control" id="steps" value="1000" min="100" max="10000">
                            </div>
                            <div class="mb-3">
                                <label for="learning-rate" class="form-label">Learning Rate</label>
                                <input type="number" class="form-control" id="learning-rate" value="0.0001" step="0.00001" min="0.00001" max="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="parameter-group">
                            <h6><i class="fas fa-network-wired me-2"></i>Network Settings</h6>
                            <div class="mb-3">
                                <label for="rank" class="form-label">LoRA Rank</label>
                                <input type="number" class="form-control" id="rank" value="16" min="4" max="128" step="4">
                            </div>
                            <div class="mb-3">
                                <label for="batch-size" class="form-label">Batch Size</label>
                                <input type="number" class="form-control" id="batch-size" value="1" min="1" max="8">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <button type="button" class="btn btn-success-custom btn-custom btn-lg" id="start-training-btn">
                        <i class="fas fa-play me-2"></i>Start Training
                    </button>
                    <button type="button" class="btn btn-danger-custom btn-custom btn-lg" id="stop-training-btn" style="display: none;">
                        <i class="fas fa-stop me-2"></i>Stop Training
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Training Progress -->
<div class="row mb-4" id="progress-section" style="display: none;">
    <div class="col-12">
        <div class="card card-custom">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Training Progress</h5>
            </div>
            <div class="card-body">
                <div id="training-progress">
                    <div class="progress progress-custom mb-3">
                        <div id="training-progress-bar" class="progress-bar progress-bar-custom" role="progressbar" style="width: 0%">0%</div>
                    </div>
                    <div id="training-message" class="text-center">
                        <p class="mb-0">Initializing training...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load available configurations
    loadConfigurations();
    
    // File upload handling
    setupFileUpload();
    
    // Event listeners
    document.getElementById('auto-caption-btn').addEventListener('click', generateCaptions);
    document.getElementById('manual-caption-btn').addEventListener('click', showManualCaptions);
    document.getElementById('start-training-btn').addEventListener('click', startTraining);
    document.getElementById('stop-training-btn').addEventListener('click', stopTraining);
});

function loadConfigurations() {
    fetch('/api/configs')
        .then(response => response.json())
        .then(configs => {
            const configList = document.getElementById('config-list');
            configList.innerHTML = '';
            
            Object.values(configs).forEach(config => {
                const configCard = createConfigCard(config);
                configList.appendChild(configCard);
            });
        })
        .catch(error => {
            console.error('Error loading configurations:', error);
            showAlert('Failed to load configurations', 'danger');
        });
}

function createConfigCard(config) {
    const col = document.createElement('div');
    col.className = 'col-md-6 col-lg-4 mb-3';
    
    const modelInfo = config.model_info || {};
    const isFlux = modelInfo.is_flux ? '<span class="badge bg-primary">FLUX</span>' : '';
    const arch = modelInfo.arch !== 'Unknown' ? `<span class="badge bg-secondary">${modelInfo.arch}</span>` : '';
    
    col.innerHTML = `
        <div class="card config-card h-100" data-config="${config.name}">
            <div class="card-body">
                <h6 class="card-title">${config.name}</h6>
                <p class="card-text small text-muted">${modelInfo.name_or_path}</p>
                <div class="mb-2">
                    ${isFlux}
                    ${arch}
                    ${modelInfo.quantize ? '<span class="badge bg-info">Quantized</span>' : ''}
                </div>
            </div>
        </div>
    `;
    
    const card = col.querySelector('.config-card');
    card.addEventListener('click', () => selectConfig(config.name, card));
    
    return col;
}

function selectConfig(configName, cardElement) {
    // Remove previous selection
    document.querySelectorAll('.config-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Select current card
    cardElement.classList.add('selected');
    selectedConfig = configName;
    
    // Load config details
    fetch(`/api/config/${configName}`)
        .then(response => response.json())
        .then(config => {
            displayConfigDetails(config);
        })
        .catch(error => {
            console.error('Error loading config details:', error);
            showAlert('Failed to load config details', 'danger');
        });
}

function displayConfigDetails(config) {
    const detailsDiv = document.getElementById('config-details');
    const contentDiv = document.getElementById('model-info-content');
    
    const modelInfo = config.model_info || {};
    
    contentDiv.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <strong>Model:</strong> ${modelInfo.name_or_path}<br>
                <strong>Architecture:</strong> ${modelInfo.arch}<br>
            </div>
            <div class="col-md-6">
                <strong>FLUX Model:</strong> ${modelInfo.is_flux ? 'Yes' : 'No'}<br>
                <strong>Quantized:</strong> ${modelInfo.quantize ? 'Yes' : 'No'}<br>
            </div>
        </div>
    `;
    
    detailsDiv.style.display = 'block';
    
    // Show captioning section if files are uploaded
    if (uploadedFiles.length > 0) {
        document.getElementById('captioning-section').style.display = 'block';
        displayUploadedImages();
    }
}

function setupFileUpload() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        handleFileUpload(files);
    });

    // File input handler
    fileInput.addEventListener('change', (e) => {
        handleFileUpload(e.target.files);
    });

    // Click handler for upload area
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });
}

function handleFileUpload(files) {
    if (files.length === 0) return;

    const formData = new FormData();
    Array.from(files).forEach(file => {
        formData.append('files', file);
    });

    // Show upload progress
    showAlert('Uploading files...', 'info');

    fetch('/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showAlert(data.error, 'danger');
            return;
        }

        currentSessionId = data.session_id;
        uploadedFiles = data.files;

        displayUploadedFiles(data.files);
        showAlert(data.message, 'success');

        // Show captioning section if config is selected
        if (selectedConfig) {
            document.getElementById('captioning-section').style.display = 'block';
            displayUploadedImages();
        }
    })
    .catch(error => {
        console.error('Upload error:', error);
        showAlert('Upload failed', 'danger');
    });
}

function displayUploadedFiles(files) {
    const fileList = document.getElementById('file-list');
    const filesContainer = document.getElementById('files-container');

    filesContainer.innerHTML = '';

    files.forEach(file => {
        const fileCard = document.createElement('div');
        fileCard.className = 'col-md-3 mb-3';

        const isImage = file.type === 'image';
        const icon = isImage ? 'fa-image' : file.type === 'video' ? 'fa-video' : 'fa-file-text';

        fileCard.innerHTML = `
            <div class="card">
                <div class="card-body text-center">
                    ${isImage ?
                        `<img src="/uploads/${currentSessionId}/${file.filename}" class="image-preview mb-2" alt="${file.filename}">` :
                        `<i class="fas ${icon} fa-3x text-muted mb-2"></i>`
                    }
                    <h6 class="card-title small">${file.filename}</h6>
                    <p class="card-text small text-muted">${formatFileSize(file.size)}</p>
                    ${isImage && file.width ? `<small class="text-muted">${file.width}x${file.height}</small>` : ''}
                </div>
            </div>
        `;

        filesContainer.appendChild(fileCard);
    });

    fileList.style.display = 'block';
}

function displayUploadedImages() {
    const captionsContainer = document.getElementById('captions-container');
    captionsContainer.innerHTML = '';

    const imageFiles = uploadedFiles.filter(file => file.type === 'image');

    imageFiles.forEach(file => {
        const captionCard = document.createElement('div');
        captionCard.className = 'col-md-6 mb-3';

        captionCard.innerHTML = `
            <div class="card">
                <div class="row g-0">
                    <div class="col-4">
                        <img src="/uploads/${currentSessionId}/${file.filename}"
                             class="img-fluid rounded-start h-100"
                             style="object-fit: cover;" alt="${file.filename}">
                    </div>
                    <div class="col-8">
                        <div class="card-body">
                            <h6 class="card-title">${file.filename}</h6>
                            <textarea class="form-control caption-input"
                                      data-filename="${file.filename}"
                                      placeholder="Enter caption for this image..."
                                      rows="3">${captions[file.filename] || ''}</textarea>
                        </div>
                    </div>
                </div>
            </div>
        `;

        captionsContainer.appendChild(captionCard);
    });

    // Add event listeners for caption inputs
    document.querySelectorAll('.caption-input').forEach(input => {
        input.addEventListener('input', (e) => {
            const filename = e.target.dataset.filename;
            captions[filename] = e.target.value;
        });
    });

    // Show training section
    document.getElementById('training-section').style.display = 'block';
}

function generateCaptions() {
    if (!currentSessionId) {
        showAlert('Please upload files first', 'warning');
        return;
    }

    const conceptSentence = document.getElementById('concept-sentence').value;
    const progressDiv = document.getElementById('caption-progress');
    const progressBar = progressDiv.querySelector('.progress-bar');

    progressDiv.style.display = 'block';
    progressBar.style.width = '0%';

    document.getElementById('auto-caption-btn').disabled = true;

    fetch('/api/caption', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            session_id: currentSessionId,
            concept_sentence: conceptSentence
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showAlert(data.error, 'danger');
            return;
        }

        captions = data.captions;

        // Update caption inputs
        document.querySelectorAll('.caption-input').forEach(input => {
            const filename = input.dataset.filename;
            if (captions[filename]) {
                input.value = captions[filename];
            }
        });

        progressBar.style.width = '100%';
        showAlert(data.message, 'success');

        setTimeout(() => {
            progressDiv.style.display = 'none';
        }, 2000);
    })
    .catch(error => {
        console.error('Captioning error:', error);
        showAlert('Captioning failed', 'danger');
    })
    .finally(() => {
        document.getElementById('auto-caption-btn').disabled = false;
    });
}

function showManualCaptions() {
    if (!currentSessionId) {
        showAlert('Please upload files first', 'warning');
        return;
    }

    displayUploadedImages();
    showAlert('You can now manually edit captions for each image', 'info');
}

function startTraining() {
    if (!currentSessionId || !selectedConfig) {
        showAlert('Please upload files and select a configuration first', 'warning');
        return;
    }

    const loraName = document.getElementById('lora-name').value.trim();
    if (!loraName) {
        showAlert('Please enter a LoRA name', 'warning');
        return;
    }

    const trainingData = {
        session_id: currentSessionId,
        config_name: selectedConfig,
        lora_name: loraName,
        concept_sentence: document.getElementById('concept-sentence').value,
        captions: captions,
        steps: parseInt(document.getElementById('steps').value),
        learning_rate: parseFloat(document.getElementById('learning-rate').value),
        rank: parseInt(document.getElementById('rank').value),
        batch_size: parseInt(document.getElementById('batch-size').value)
    };

    document.getElementById('start-training-btn').style.display = 'none';
    document.getElementById('stop-training-btn').style.display = 'inline-block';
    document.getElementById('progress-section').style.display = 'block';

    fetch('/api/train', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(trainingData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showAlert(data.error, 'danger');
            resetTrainingUI();
            return;
        }

        showAlert(data.message, 'success');
        startProgressPolling();
    })
    .catch(error => {
        console.error('Training error:', error);
        showAlert('Failed to start training', 'danger');
        resetTrainingUI();
    });
}

function stopTraining() {
    fetch('/api/training/stop', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        showAlert(data.message, 'info');
        resetTrainingUI();
    })
    .catch(error => {
        console.error('Stop training error:', error);
        showAlert('Failed to stop training', 'danger');
    });
}

function resetTrainingUI() {
    document.getElementById('start-training-btn').style.display = 'inline-block';
    document.getElementById('stop-training-btn').style.display = 'none';
    document.getElementById('progress-section').style.display = 'none';
}

function startProgressPolling() {
    const progressBar = document.getElementById('training-progress-bar');
    const messageDiv = document.getElementById('training-message');

    const pollInterval = setInterval(() => {
        fetch('/api/training/status')
            .then(response => response.json())
            .then(data => {
                if (!data.is_training) {
                    clearInterval(pollInterval);
                    resetTrainingUI();

                    if (data.error) {
                        showAlert('Training failed: ' + data.error, 'danger');
                    } else if (data.progress === 100) {
                        showAlert('Training completed successfully!', 'success');
                    }
                } else {
                    progressBar.style.width = data.progress + '%';
                    progressBar.textContent = data.progress + '%';
                    messageDiv.innerHTML = `<p class="mb-0">${data.message}</p>`;
                }
            })
            .catch(error => {
                console.error('Progress polling error:', error);
            });
    }, 2000);
}
</script>
{% endblock %}
