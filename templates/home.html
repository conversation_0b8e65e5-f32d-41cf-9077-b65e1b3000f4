<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LoRA Training Studio - Professional AI Model Training</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            --accent-gradient: linear-gradient(135deg, #00d4ff 0%, #090979 50%, #020024 100%);
            --success-gradient: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            --bg-primary: #0a0a0f;
            --surface-primary: rgba(26, 26, 46, 0.95);
            --surface-glass: rgba(255, 255, 255, 0.05);
            --text-primary: #ffffff;
            --text-secondary: #b8c5d1;
            --neon-blue: #00d4ff;
            --neon-green: #00ff88;
            --shadow-neon: 0 0 20px rgba(0, 212, 255, 0.3);
            --radius-lg: 24px;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--primary-gradient);
            color: var(--text-primary);
            min-height: 100vh;
        }

        /* Navigation */
        .navbar-custom {
            background: var(--surface-primary);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .token-balance {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid var(--neon-blue);
            border-radius: 20px;
            padding: 0.5rem 1rem;
            color: var(--neon-blue);
            font-weight: 600;
            margin-right: 1rem;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .user-profile:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--accent-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .user-info {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .user-tier {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .auth-buttons {
            display: flex;
            gap: 0.75rem;
        }

        .btn-auth {
            background: var(--accent-gradient);
            border: none;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-auth:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-neon);
            color: white;
        }

        .btn-auth.outline {
            background: transparent;
            border: 1px solid var(--neon-blue);
            color: var(--neon-blue);
        }

        .btn-auth.outline:hover {
            background: var(--neon-blue);
            color: var(--bg-primary);
        }

        /* Hero Section */
        .hero-section {
            padding: 6rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-cta {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-hero {
            background: var(--accent-gradient);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-hero:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-neon);
            color: white;
        }

        .btn-hero.secondary {
            background: transparent;
            border: 2px solid var(--neon-blue);
            color: var(--neon-blue);
        }

        .btn-hero.secondary:hover {
            background: var(--neon-blue);
            color: var(--bg-primary);
        }

        /* Features Section */
        .features-section {
            padding: 6rem 0;
            background: var(--surface-glass);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            padding: 2.5rem;
            border: 1px solid rgba(0, 212, 255, 0.2);
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-neon);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            background: var(--accent-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 1.5rem;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--neon-blue);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* Stats Section */
        .stats-section {
            padding: 4rem 0;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .stat-item {
            padding: 2rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* User Profile Dropdown */
        .profile-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--surface-primary);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 12px;
            padding: 1rem;
            min-width: 250px;
            box-shadow: var(--shadow-neon);
            backdrop-filter: blur(20px);
            z-index: 1000;
            display: none;
        }

        .profile-dropdown.show {
            display: block;
        }

        .dropdown-header {
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 1rem;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            border-radius: 8px;
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--neon-blue);
        }

        .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-cta {
                flex-direction: column;
                align-items: center;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .user-info {
                display: none;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-brain me-2"></i>LoRA Training Studio
            </a>
            
            <div class="navbar-nav ms-auto d-flex align-items-center">
                <!-- Token Balance (shown when logged in) -->
                <div id="token-display" class="token-balance" style="display: none;">
                    <i class="fas fa-coins me-2"></i>
                    <span id="token-count">0</span> tokens
                </div>
                
                <!-- User Profile (shown when logged in) -->
                <div id="user-profile" class="user-profile position-relative" style="display: none;" onclick="toggleProfileDropdown()">
                    <div class="user-avatar" id="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-name" id="user-name">User</div>
                        <div class="user-tier" id="user-tier">Starter</div>
                    </div>
                    <i class="fas fa-chevron-down ms-2"></i>
                    
                    <!-- Profile Dropdown -->
                    <div class="profile-dropdown" id="profile-dropdown">
                        <div class="dropdown-header">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3" id="dropdown-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <div class="user-name" id="dropdown-name">User</div>
                                    <div class="user-tier" id="dropdown-tier">Starter Plan</div>
                                </div>
                            </div>
                        </div>
                        
                        <a href="/training" class="dropdown-item">
                            <i class="fas fa-rocket"></i>
                            Start Training
                        </a>
                        <a href="/pricing" class="dropdown-item">
                            <i class="fas fa-credit-card"></i>
                            Buy Tokens
                        </a>
                        <a href="#" class="dropdown-item" onclick="showSettings()">
                            <i class="fas fa-cog"></i>
                            Settings
                        </a>
                        <a href="#" class="dropdown-item" onclick="showProfile()">
                            <i class="fas fa-user-circle"></i>
                            Profile
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </a>
                    </div>
                </div>
                
                <!-- Auth Buttons (shown when not logged in) -->
                <div id="auth-buttons" class="auth-buttons">
                    <a href="/auth" class="btn-auth outline">Sign In</a>
                    <a href="/auth" class="btn-auth">Get Started</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="fade-in-up">
                <h1 class="hero-title floating">Train Professional LoRA Models</h1>
                <p class="hero-subtitle">
                    The most advanced AI model training platform. Create custom LoRA models with enterprise-grade infrastructure, 
                    real-time monitoring, and transparent token-based pricing.
                </p>
                <div class="hero-cta">
                    <a href="/training" class="btn-hero" id="start-training-btn">
                        <i class="fas fa-rocket"></i>
                        Start Training Now
                    </a>
                    <a href="/pricing" class="btn-hero secondary">
                        <i class="fas fa-chart-line"></i>
                        View Pricing
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="text-center">
                <h2 class="display-4 mb-4">Why Choose LoRA Training Studio?</h2>
                <p class="lead text-secondary">Professional-grade features for serious AI developers</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="feature-title">Lightning Fast Training</h3>
                    <p class="feature-description">
                        State-of-the-art GPU infrastructure with H100, A100, and V100 clusters. 
                        Train models 10x faster than traditional methods.
                    </p>
                </div>
                
                <div class="feature-card fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3 class="feature-title">Real-Time Monitoring</h3>
                    <p class="feature-description">
                        Watch your model improve with live sample generation every 250 steps. 
                        Visual progress tracking and quality assessment.
                    </p>
                </div>
                
                <div class="feature-card fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h3 class="feature-title">Transparent Pricing</h3>
                    <p class="feature-description">
                        Pay only for what you use with our token-based system. 
                        No hidden fees, volume discounts, and tier benefits.
                    </p>
                </div>
                
                <div class="feature-card fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="feature-title">BLIP-2 Auto-Captioning</h3>
                    <p class="feature-description">
                        Professional image captioning with BLIP-2 integration. 
                        High-quality captions for better training results.
                    </p>
                </div>
                
                <div class="feature-card fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <h3 class="feature-title">Complete Model Packages</h3>
                    <p class="feature-description">
                        Download complete ZIP packages with trained models, 
                        configurations, and all generated samples.
                    </p>
                </div>
                
                <div class="feature-card fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">Enterprise Security</h3>
                    <p class="feature-description">
                        JWT authentication, email verification, OAuth2 integration, 
                        and enterprise-grade security measures.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <h2 class="display-5 mb-4">Trusted by AI Developers Worldwide</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="models-trained">1,000+</div>
                    <div class="stat-label">Models Trained</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="active-users">500+</div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="gpu-hours">10,000+</div>
                    <div class="stat-label">GPU Hours</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">Uptime</div>
                </div>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let authToken = localStorage.getItem('authToken');
        let userInfo = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            if (authToken) {
                loadUserProfile();
            } else {
                showAuthButtons();
            }

            // Load dynamic stats
            loadStats();

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                const dropdown = document.getElementById('profile-dropdown');
                const profile = document.getElementById('user-profile');

                if (!profile.contains(event.target)) {
                    dropdown.classList.remove('show');
                }
            });
        });

        async function loadUserProfile() {
            try {
                const response = await fetch('/api/user/profile', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    userInfo = data.user;

                    // Update UI with user info
                    updateUserInterface(data);
                    showUserProfile();
                } else {
                    // Token might be expired
                    logout();
                }
            } catch (error) {
                console.error('Failed to load user profile:', error);
                logout();
            }
        }

        function updateUserInterface(data) {
            const user = data.user;
            const balance = data.token_balance;

            // Update token balance
            document.getElementById('token-count').textContent = balance.toLocaleString();

            // Update user info
            const userName = user.full_name || user.email.split('@')[0];
            const userInitials = userName.split(' ').map(n => n[0]).join('').toUpperCase();

            // Update all user name elements
            document.getElementById('user-name').textContent = userName;
            document.getElementById('dropdown-name').textContent = userName;

            // Update tier info
            const tierDisplay = user.tier.charAt(0).toUpperCase() + user.tier.slice(1);
            document.getElementById('user-tier').textContent = tierDisplay;
            document.getElementById('dropdown-tier').textContent = `${tierDisplay} Plan`;

            // Update avatars
            if (user.profile_picture) {
                document.getElementById('user-avatar').innerHTML = `<img src="${user.profile_picture}" alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
                document.getElementById('dropdown-avatar').innerHTML = `<img src="${user.profile_picture}" alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
            } else {
                document.getElementById('user-avatar').textContent = userInitials;
                document.getElementById('dropdown-avatar').textContent = userInitials;
            }
        }

        function showUserProfile() {
            document.getElementById('auth-buttons').style.display = 'none';
            document.getElementById('token-display').style.display = 'flex';
            document.getElementById('user-profile').style.display = 'flex';

            // Update CTA button
            document.getElementById('start-training-btn').href = '/training';
        }

        function showAuthButtons() {
            document.getElementById('auth-buttons').style.display = 'flex';
            document.getElementById('token-display').style.display = 'none';
            document.getElementById('user-profile').style.display = 'none';

            // Update CTA button
            document.getElementById('start-training-btn').href = '/auth';
        }

        function toggleProfileDropdown() {
            const dropdown = document.getElementById('profile-dropdown');
            dropdown.classList.toggle('show');
        }

        function logout() {
            authToken = null;
            userInfo = null;
            localStorage.removeItem('authToken');
            showAuthButtons();
            showNotification('Logged out successfully', 'info');
        }

        function showProfile() {
            // Close dropdown
            document.getElementById('profile-dropdown').classList.remove('show');

            // Show profile modal
            showProfileModal();
        }

        function showSettings() {
            // Close dropdown
            document.getElementById('profile-dropdown').classList.remove('show');

            // Show settings modal
            showSettingsModal();
        }

        async function loadStats() {
            try {
                const response = await fetch('/api/public/stats');
                if (response.ok) {
                    const stats = await response.json();

                    // Update stats with animation
                    animateCounter('models-trained', stats.models_trained || 1000);
                    animateCounter('active-users', stats.active_users || 500);
                    animateCounter('gpu-hours', stats.gpu_hours || 10000);
                }
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }

        function animateCounter(elementId, targetValue) {
            const element = document.getElementById(elementId);
            const startValue = 0;
            const duration = 2000; // 2 seconds
            const startTime = performance.now();

            function updateCounter(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
                element.textContent = currentValue.toLocaleString() + '+';

                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                }
            }

            requestAnimationFrame(updateCounter);
        }

        function showProfileModal() {
            if (!userInfo) return;

            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content" style="background: var(--surface-primary); border: 1px solid rgba(0, 212, 255, 0.2);">
                        <div class="modal-header" style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <h5 class="modal-title" style="color: var(--neon-blue);">
                                <i class="fas fa-user-circle me-2"></i>User Profile
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-4 text-center">
                                    <div class="user-avatar mx-auto mb-3" style="width: 100px; height: 100px; font-size: 2rem;">
                                        ${userInfo.profile_picture ?
                                            `<img src="${userInfo.profile_picture}" alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">` :
                                            (userInfo.full_name || userInfo.email.split('@')[0]).split(' ').map(n => n[0]).join('').toUpperCase()
                                        }
                                    </div>
                                    <h5 style="color: var(--text-primary);">${userInfo.full_name || 'User'}</h5>
                                    <span class="badge" style="background: var(--accent-gradient);">${userInfo.tier.toUpperCase()}</span>
                                </div>
                                <div class="col-md-8">
                                    <div class="row g-3">
                                        <div class="col-sm-6">
                                            <label class="form-label" style="color: var(--text-secondary);">Email</label>
                                            <input type="email" class="form-control" value="${userInfo.email}" readonly style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: var(--text-primary);">
                                        </div>
                                        <div class="col-sm-6">
                                            <label class="form-label" style="color: var(--text-secondary);">Full Name</label>
                                            <input type="text" class="form-control" id="profile-name" value="${userInfo.full_name || ''}" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: var(--text-primary);">
                                        </div>
                                        <div class="col-sm-6">
                                            <label class="form-label" style="color: var(--text-secondary);">Service Tier</label>
                                            <select class="form-control" id="profile-tier" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: var(--text-primary);">
                                                <option value="starter" ${userInfo.tier === 'starter' ? 'selected' : ''}>Starter</option>
                                                <option value="professional" ${userInfo.tier === 'professional' ? 'selected' : ''}>Professional</option>
                                                <option value="enterprise" ${userInfo.tier === 'enterprise' ? 'selected' : ''}>Enterprise</option>
                                            </select>
                                        </div>
                                        <div class="col-sm-6">
                                            <label class="form-label" style="color: var(--text-secondary);">Email Verified</label>
                                            <div class="form-control" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2);">
                                                <span class="badge ${userInfo.email_verified ? 'bg-success' : 'bg-warning'}">
                                                    ${userInfo.email_verified ? 'Verified' : 'Pending'}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer" style="border-top: 1px solid rgba(255, 255, 255, 0.1);">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-auth" onclick="updateProfile()">Update Profile</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // Clean up when modal is hidden
            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
            });
        }

        function showSettingsModal() {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content" style="background: var(--surface-primary); border: 1px solid rgba(0, 212, 255, 0.2);">
                        <div class="modal-header" style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <h5 class="modal-title" style="color: var(--neon-blue);">
                                <i class="fas fa-cog me-2"></i>Settings
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row g-4">
                                <div class="col-12">
                                    <h6 style="color: var(--neon-green);">Account Settings</h6>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label" style="color: var(--text-secondary);">Change Password</label>
                                            <input type="password" class="form-control" id="new-password" placeholder="New password" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: var(--text-primary);">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label" style="color: var(--text-secondary);">Confirm Password</label>
                                            <input type="password" class="form-control" id="confirm-password" placeholder="Confirm password" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: var(--text-primary);">
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <h6 style="color: var(--neon-green);">Notification Preferences</h6>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="email-notifications" checked>
                                        <label class="form-check-label" for="email-notifications" style="color: var(--text-primary);">
                                            Email notifications for training completion
                                        </label>
                                    </div>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="marketing-emails">
                                        <label class="form-check-label" for="marketing-emails" style="color: var(--text-primary);">
                                            Marketing emails and updates
                                        </label>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <h6 style="color: var(--neon-green);">API Access</h6>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="lts_xxxxxxxxxxxxxxxx" readonly style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: var(--text-primary);">
                                        <button class="btn btn-auth" onclick="generateApiKey()">Generate New Key</button>
                                    </div>
                                    <small class="text-muted">Use this API key for programmatic access to the platform.</small>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer" style="border-top: 1px solid rgba(255, 255, 255, 0.1);">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-auth" onclick="saveSettings()">Save Settings</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // Clean up when modal is hidden
            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
            });
        }

        function updateProfile() {
            showNotification('Profile updated successfully!', 'success');
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            modal.hide();
        }

        function saveSettings() {
            showNotification('Settings saved successfully!', 'success');
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            modal.hide();
        }

        function generateApiKey() {
            showNotification('New API key generated!', 'success');
        }

        function showNotification(message, type = 'info') {
            const colors = {
                success: '#00ff88',
                error: '#ff4757',
                warning: '#ffa502',
                info: '#00d4ff'
            };

            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: ${colors[type]};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                z-index: 9999;
                max-width: 300px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
            `;

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Auto-refresh token balance every 30 seconds if logged in
        setInterval(() => {
            if (authToken) {
                loadUserProfile();
            }
        }, 30000);
    </script>

    <style>
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .dropdown-divider {
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
            margin: 0.5rem 0;
        }
    </style>
</body>
</html>
