<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .model-option {
            margin: 10px 0;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .model-option:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .model-option.selected {
            border-color: #28a745;
            background: #f8fff8;
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 JavaScript Test Page</h1>
        
        <div id="test-status" style="padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; margin: 20px 0;">
            ❌ JavaScript not loaded yet...
        </div>

        <h2>Model Selection</h2>
        <div id="model-options">
            <p style="color: red;">Models should appear here...</p>
        </div>

        <h2>File Upload</h2>
        <div class="upload-area" id="upload-area">
            <h3>📁 Drag & Drop Test Area</h3>
            <p>Click here or drag files to test upload</p>
            <input type="file" id="file-input" multiple accept="image/*" style="display: none;">
        </div>

        <div id="upload-results"></div>
    </div>

    <script>
        console.log('🚀 Test page JavaScript loading...');

        // Immediate test
        document.getElementById('test-status').innerHTML = '✅ JavaScript is working!';
        document.getElementById('test-status').style.background = '#d4edda';
        document.getElementById('test-status').style.borderColor = '#c3e6cb';

        // Wait for DOM
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ DOM loaded');

            // Test model loading
            loadTestModels();

            // Test file upload
            setupTestUpload();
        });

        function loadTestModels() {
            console.log('📋 Loading test models...');
            
            const container = document.getElementById('model-options');
            const models = [
                { key: 'flux_dev', name: 'FLUX.1 Dev (24GB)', desc: 'High-quality - RECOMMENDED' },
                { key: 'flux_schnell', name: 'FLUX.1 Schnell (24GB)', desc: 'Fast generation' },
                { key: 'hidream', name: 'HiDream (48GB)', desc: 'High-resolution' },
                { key: 'wan21_1b', name: 'WAN 2.1 1B (24GB)', desc: 'Efficient model' },
                { key: 'wan21_14b', name: 'WAN 2.1 14B (24GB)', desc: 'Large model' }
            ];

            let html = '';
            models.forEach((model, index) => {
                const isRecommended = index === 0;
                html += `
                    <div class="model-option ${isRecommended ? 'selected' : ''}" 
                         onclick="selectTestModel('${model.key}')" 
                         data-model="${model.key}">
                        <strong>${model.name}</strong><br>
                        <small>${model.desc}</small>
                        ${isRecommended ? '<br><span style="color: #28a745;">⭐ RECOMMENDED</span>' : ''}
                    </div>
                `;
            });

            container.innerHTML = html;
            console.log('✅ Models loaded');
        }

        function selectTestModel(modelKey) {
            console.log('🎯 Selected model:', modelKey);
            
            // Update selection
            document.querySelectorAll('.model-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector(`[data-model="${modelKey}"]`).classList.add('selected');
            
            alert(`Selected: ${modelKey}`);
        }

        function setupTestUpload() {
            console.log('📁 Setting up test upload...');
            
            const uploadArea = document.getElementById('upload-area');
            const fileInput = document.getElementById('file-input');
            const results = document.getElementById('upload-results');

            uploadArea.addEventListener('click', () => fileInput.click());

            fileInput.addEventListener('change', function(e) {
                const files = e.target.files;
                console.log('📁 Files selected:', files.length);
                
                let html = '<h3>Selected Files:</h3>';
                Array.from(files).forEach(file => {
                    html += `<p>📄 ${file.name} (${Math.round(file.size/1024)}KB)</p>`;
                });
                results.innerHTML = html;
            });

            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#007bff';
                uploadArea.style.background = '#f8f9fa';
            });

            uploadArea.addEventListener('dragleave', function() {
                uploadArea.style.borderColor = '#ddd';
                uploadArea.style.background = '';
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#ddd';
                uploadArea.style.background = '';
                
                const files = e.dataTransfer.files;
                console.log('📁 Files dropped:', files.length);
                
                let html = '<h3>Dropped Files:</h3>';
                Array.from(files).forEach(file => {
                    html += `<p>📄 ${file.name} (${Math.round(file.size/1024)}KB)</p>`;
                });
                results.innerHTML = html;
            });

            console.log('✅ Upload setup complete');
        }
    </script>
</body>
</html>
