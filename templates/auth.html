{% extends "base.html" %}

{% block title %}LoRA Training Studio - Authentication{% endblock %}

{% block extra_css %}
<style>
        :root {
            --primary-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            --accent-gradient: linear-gradient(135deg, #00d4ff 0%, #090979 50%, #020024 100%);
            --success-gradient: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            --bg-primary: #0a0a0f;
            --surface-primary: rgba(26, 26, 46, 0.95);
            --surface-glass: rgba(255, 255, 255, 0.05);
            --text-primary: #ffffff;
            --text-secondary: #b8c5d1;
            --neon-blue: #00d4ff;
            --neon-green: #00ff88;
            --shadow-neon: 0 0 20px rgba(0, 212, 255, 0.3);
            --radius-lg: 24px;
        }

        /* Auth page specific styles */
        .auth-page {
            min-height: calc(100vh - 80px);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .auth-container {
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            padding: 3rem;
            border: 1px solid rgba(0, 212, 255, 0.2);
            backdrop-filter: blur(20px);
            box-shadow: var(--shadow-neon);
            width: 100%;
            max-width: 450px;
            position: relative;
            overflow: hidden;
        }

        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-gradient);
        }

        .auth-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .auth-logo {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }

        .auth-subtitle {
            color: var(--text-secondary);
            font-size: 0.95rem;
        }

        .auth-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 0.25rem;
            margin-bottom: 2rem;
        }

        .auth-tab {
            flex: 1;
            background: none;
            border: none;
            color: var(--text-secondary);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .auth-tab.active {
            background: var(--accent-gradient);
            color: white;
            box-shadow: var(--shadow-neon);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-control {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: var(--text-primary);
            padding: 0.875rem 1rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--neon-blue);
            box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.2);
            color: var(--text-primary);
            outline: none;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .btn-auth {
            width: 100%;
            background: var(--accent-gradient);
            border: none;
            color: white;
            padding: 1rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .btn-auth:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-neon);
            color: white;
        }

        .btn-auth:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-google {
            width: 100%;
            background: white;
            border: 1px solid #ddd;
            color: #333;
            padding: 0.875rem 1rem;
            border-radius: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .btn-google:hover {
            background: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .divider {
            display: flex;
            align-items: center;
            margin: 1.5rem 0;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: rgba(255, 255, 255, 0.2);
        }

        .divider span {
            padding: 0 1rem;
        }

        .auth-links {
            text-align: center;
            margin-top: 1.5rem;
        }

        .auth-link {
            color: var(--neon-blue);
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .auth-link:hover {
            color: var(--neon-green);
            text-decoration: underline;
        }

        .verification-section {
            display: none;
            text-align: center;
        }

        .verification-code-input {
            font-size: 1.5rem;
            text-align: center;
            letter-spacing: 0.5rem;
            font-weight: 600;
            margin: 1rem 0;
        }

        .success-message {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid var(--neon-green);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            color: var(--neon-green);
            text-align: center;
        }

        .error-message {
            background: rgba(255, 71, 87, 0.1);
            border: 1px solid #ff4757;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            color: #ff4757;
            text-align: center;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .tier-selection {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .tier-option {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 0.75rem 0.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
        }

        .tier-option:hover {
            border-color: var(--neon-blue);
        }

        .tier-option.selected {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--neon-blue);
            color: var(--neon-blue);
        }

        .tier-price {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        @media (max-width: 480px) {
            .auth-container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }
            
            .tier-selection {
                grid-template-columns: 1fr;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="auth-page">
    <div class="auth-container">
        <div class="auth-header">
            <div class="auth-logo">LoRA Studio</div>
            <div class="auth-subtitle">Professional AI Model Training Platform</div>
        </div>

        <!-- Tab Navigation -->
        <div class="auth-tabs">
            <button class="auth-tab active" onclick="switchTab('login')">Sign In</button>
            <button class="auth-tab" onclick="switchTab('signup')">Sign Up</button>
        </div>

        <!-- Messages -->
        <div id="success-message" class="success-message" style="display: none;"></div>
        <div id="error-message" class="error-message" style="display: none;"></div>

        <!-- Login Form -->
        <div id="login-form" class="auth-form">
            <!-- Google Sign In -->
            <button class="btn-google" onclick="signInWithGoogle()">
                <svg width="20" height="20" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Continue with Google
            </button>

            <div class="divider">
                <span>or</span>
            </div>

            <form onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label class="form-label">Email Address</label>
                    <input type="email" class="form-control" id="login-email" placeholder="Enter your email" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-control" id="login-password" placeholder="Enter your password" required>
                </div>
                <button type="submit" class="btn-auth">
                    <span class="btn-text">Sign In</span>
                    <div class="loading-spinner"></div>
                </button>
            </form>

            <div class="auth-links">
                <a href="#" class="auth-link" onclick="showForgotPassword()">Forgot your password?</a>
            </div>
        </div>

        <!-- Signup Form -->
        <div id="signup-form" class="auth-form" style="display: none;">
            <!-- Google Sign Up -->
            <button class="btn-google" onclick="signUpWithGoogle()">
                <svg width="20" height="20" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Sign up with Google
            </button>

            <div class="divider">
                <span>or</span>
            </div>

            <form onsubmit="handleSignup(event)">
                <div class="form-group">
                    <label class="form-label">Full Name</label>
                    <input type="text" class="form-control" id="signup-name" placeholder="Enter your full name" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Email Address</label>
                    <input type="email" class="form-control" id="signup-email" placeholder="Enter your email" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-control" id="signup-password" placeholder="Create a password" required minlength="8">
                </div>
                <div class="form-group">
                    <label class="form-label">Confirm Password</label>
                    <input type="password" class="form-control" id="signup-confirm" placeholder="Confirm your password" required>
                </div>
                
                <!-- Service Tier Selection -->
                <div class="form-group">
                    <label class="form-label">Choose Your Plan</label>
                    <div class="tier-selection">
                        <div class="tier-option selected" data-tier="starter">
                            <div>Starter</div>
                            <div class="tier-price">$10/1K tokens</div>
                        </div>
                        <div class="tier-option" data-tier="professional">
                            <div>Professional</div>
                            <div class="tier-price">$8.5/1K tokens</div>
                        </div>
                        <div class="tier-option" data-tier="enterprise">
                            <div>Enterprise</div>
                            <div class="tier-price">$7/1K tokens</div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn-auth">
                    <span class="btn-text">Create Account</span>
                    <div class="loading-spinner"></div>
                </button>
            </form>
        </div>

        <!-- Email Verification -->
        <div id="verification-form" class="verification-section">
            <h4>Verify Your Email</h4>
            <p>We've sent a 6-digit code to <span id="verification-email"></span></p>
            
            <div class="form-group">
                <input type="text" class="form-control verification-code-input" id="verification-code" 
                       placeholder="000000" maxlength="6" pattern="[0-9]{6}">
            </div>
            
            <button class="btn-auth" onclick="verifyEmail()">
                <span class="btn-text">Verify Email</span>
                <div class="loading-spinner"></div>
            </button>
            
            <div class="auth-links">
                <a href="#" class="auth-link" onclick="resendVerification()">Resend code</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentTab = 'login';
    let selectedTier = 'starter';
    let pendingVerificationEmail = '';

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Tier selection
            document.querySelectorAll('.tier-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.tier-option').forEach(o => o.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedTier = this.dataset.tier;
                });
            });

            // Auto-format verification code
            document.getElementById('verification-code').addEventListener('input', function(e) {
                this.value = this.value.replace(/\D/g, '').substring(0, 6);
            });
        });

        function switchTab(tab) {
            currentTab = tab;

            // Update tab buttons
            document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');

            // Show/hide forms
            document.getElementById('login-form').style.display = tab === 'login' ? 'block' : 'none';
            document.getElementById('signup-form').style.display = tab === 'signup' ? 'block' : 'none';
            document.getElementById('verification-form').style.display = 'none';

            clearMessages();
        }

        async function handleLogin(event) {
            event.preventDefault();

            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;

            setLoading(true);
            clearMessages();

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (data.success) {
                    localStorage.setItem('authToken', data.token);
                    showSuccess('Login successful! Redirecting...');

                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1500);
                } else {
                    showError(data.error || 'Login failed');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            } finally {
                setLoading(false);
            }
        }

        async function handleSignup(event) {
            event.preventDefault();

            const name = document.getElementById('signup-name').value;
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;
            const confirmPassword = document.getElementById('signup-confirm').value;

            // Validation
            if (password !== confirmPassword) {
                showError('Passwords do not match');
                return;
            }

            if (password.length < 8) {
                showError('Password must be at least 8 characters');
                return;
            }

            setLoading(true);
            clearMessages();

            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email,
                        password,
                        full_name: name,
                        tier: selectedTier
                    })
                });

                const data = await response.json();

                if (data.success) {
                    pendingVerificationEmail = email;
                    showVerificationForm();
                    showSuccess('Account created! Please check your email for verification code.');
                } else {
                    showError(data.error || 'Registration failed');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            } finally {
                setLoading(false);
            }
        }

        async function verifyEmail() {
            const code = document.getElementById('verification-code').value;

            if (code.length !== 6) {
                showError('Please enter a 6-digit code');
                return;
            }

            setLoading(true);
            clearMessages();

            try {
                const response = await fetch('/api/auth/verify-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: pendingVerificationEmail,
                        code: code
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showSuccess('Email verified! You can now sign in.');
                    setTimeout(() => {
                        switchTab('login');
                        document.getElementById('login-email').value = pendingVerificationEmail;
                    }, 2000);
                } else {
                    showError(data.error || 'Verification failed');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            } finally {
                setLoading(false);
            }
        }

        async function resendVerification() {
            if (!pendingVerificationEmail) return;

            try {
                const response = await fetch('/api/auth/resend-verification', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: pendingVerificationEmail })
                });

                const data = await response.json();

                if (data.success) {
                    showSuccess('Verification code resent!');
                } else {
                    showError(data.error || 'Failed to resend code');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        }

        async function signInWithGoogle() {
            try {
                const response = await fetch('/api/auth/google/url');
                const data = await response.json();

                if (data.authorization_url) {
                    window.location.href = data.authorization_url;
                } else {
                    showError('Google authentication not available');
                }
            } catch (error) {
                showError('Failed to initialize Google sign-in');
            }
        }

        function signUpWithGoogle() {
            signInWithGoogle(); // Same flow for signup
        }

        function showVerificationForm() {
            document.getElementById('login-form').style.display = 'none';
            document.getElementById('signup-form').style.display = 'none';
            document.getElementById('verification-form').style.display = 'block';
            document.getElementById('verification-email').textContent = pendingVerificationEmail;
        }

        function showForgotPassword() {
            const email = prompt('Enter your email address:');
            if (email) {
                fetch('/api/auth/forgot-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSuccess('Password reset email sent!');
                    } else {
                        showError(data.error || 'Failed to send reset email');
                    }
                })
                .catch(error => {
                    showError('Network error. Please try again.');
                });
            }
        }

        function setLoading(loading) {
            const buttons = document.querySelectorAll('.btn-auth');
            const spinners = document.querySelectorAll('.loading-spinner');
            const texts = document.querySelectorAll('.btn-text');

            buttons.forEach(btn => btn.disabled = loading);
            spinners.forEach(spinner => spinner.style.display = loading ? 'inline-block' : 'none');
            texts.forEach(text => text.style.display = loading ? 'none' : 'inline');
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('error-message').style.display = 'none';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('success-message').style.display = 'none';
        }

        function clearMessages() {
            document.getElementById('success-message').style.display = 'none';
            document.getElementById('error-message').style.display = 'none';
        }

        // Check if user is already logged in
        if (localStorage.getItem('authToken')) {
            window.location.href = '/';
        }
    </script>
{% endblock %}
