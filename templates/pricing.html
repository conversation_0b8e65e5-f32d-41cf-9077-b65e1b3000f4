{% extends "base.html" %}

{% block title %}LoRA Training Studio - Professional Pricing{% endblock %}

{% block extra_css %}
<style>
        :root {
            --primary-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            --accent-gradient: linear-gradient(135deg, #00d4ff 0%, #090979 50%, #020024 100%);
            --success-gradient: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            --bg-primary: #0a0a0f;
            --surface-primary: rgba(26, 26, 46, 0.95);
            --surface-glass: rgba(255, 255, 255, 0.05);
            --text-primary: #ffffff;
            --text-secondary: #b8c5d1;
            --neon-blue: #00d4ff;
            --neon-green: #00ff88;
            --shadow-neon: 0 0 20px rgba(0, 212, 255, 0.3);
            --radius-lg: 24px;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--primary-gradient);
            color: var(--text-primary);
            min-height: 100vh;
        }

        .pricing-hero {
            text-align: center;
            padding: 4rem 0;
            background: var(--surface-glass);
            backdrop-filter: blur(20px);
        }

        .pricing-hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }

        .pricing-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            padding: 4rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .pricing-card {
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            padding: 2.5rem;
            border: 1px solid rgba(0, 212, 255, 0.2);
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .pricing-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-neon);
            border-color: var(--neon-blue);
        }

        .pricing-card.popular {
            border-color: var(--neon-green);
            box-shadow: 0 0 30px rgba(0, 255, 136, 0.3);
        }

        .pricing-card.popular::before {
            content: 'MOST POPULAR';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: var(--success-gradient);
            color: white;
            text-align: center;
            padding: 0.5rem;
            font-weight: 600;
            font-size: 0.8rem;
        }

        .tier-name {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--neon-blue);
        }

        .tier-price {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .tier-discount {
            background: var(--success-gradient);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1.5rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 2rem 0;
        }

        .feature-list li {
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list i {
            color: var(--neon-green);
            margin-right: 0.75rem;
            width: 20px;
        }

        .btn-pricing {
            background: var(--accent-gradient);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: var(--radius-lg);
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-pricing:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-neon);
            color: white;
        }

        .token-packages {
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            padding: 3rem;
            margin: 4rem auto;
            max-width: 1000px;
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

        .package-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .package-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .package-card:hover {
            transform: translateY(-5px);
            border-color: var(--neon-blue);
        }

        .package-card.popular {
            border-color: var(--neon-green);
            background: rgba(0, 255, 136, 0.1);
        }

        .cost-calculator {
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            padding: 3rem;
            margin: 4rem auto;
            max-width: 800px;
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

        .calculator-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: var(--text-primary);
            padding: 0.75rem 1rem;
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--neon-blue);
            box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.2);
            color: var(--text-primary);
        }

        .cost-result {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid var(--neon-blue);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
        }

        .cost-amount {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--neon-blue);
            margin-bottom: 0.5rem;
        }

        .cost-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .breakdown-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }

        .auth-section {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .btn-auth {
            background: var(--surface-primary);
            border: 1px solid var(--neon-blue);
            color: var(--neon-blue);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            margin-left: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-auth:hover {
            background: var(--neon-blue);
            color: var(--bg-primary);
        }

        @media (max-width: 768px) {
            .pricing-hero h1 {
                font-size: 2.5rem;
            }
            
            .pricing-cards {
                grid-template-columns: 1fr;
                padding: 2rem 1rem;
            }
            
            .package-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
{% endblock %}

{% block content %}
    <!-- Authentication Section -->
    <div class="auth-section">
        <div id="auth-buttons">
            <button class="btn btn-auth" onclick="showLogin()">Login</button>
            <button class="btn btn-auth" onclick="showRegister()">Sign Up</button>
        </div>
        <div id="user-info" style="display: none;">
            <span class="text-white me-3">Balance: <span id="token-balance">0</span> tokens</span>
            <button class="btn btn-auth" onclick="logout()">Logout</button>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="pricing-hero">
        <div class="container">
            <h1>Professional LoRA Training</h1>
            <p class="lead text-secondary">Advanced AI model training with transparent token-based pricing</p>
            <p class="text-muted">Pay only for what you use • No hidden fees • Enterprise-grade infrastructure</p>
        </div>
    </section>

    <!-- Pricing Tiers -->
    <section class="pricing-cards">
        <div class="pricing-card">
            <div class="tier-name">Starter</div>
            <div class="tier-price">$10<small>/1K tokens</small></div>
            <div class="tier-discount" style="background: #6c757d;">No Discount</div>
            <ul class="feature-list">
                <li><i class="fas fa-check"></i> Basic LoRA training</li>
                <li><i class="fas fa-check"></i> Standard GPU access</li>
                <li><i class="fas fa-check"></i> Community support</li>
                <li><i class="fas fa-check"></i> Basic sample generation</li>
                <li><i class="fas fa-times text-muted"></i> Max 2K training steps</li>
                <li><i class="fas fa-times text-muted"></i> Max 100 images per dataset</li>
            </ul>
            <button class="btn btn-pricing" onclick="selectTier('starter')">Get Started</button>
        </div>

        <div class="pricing-card popular">
            <div class="tier-name">Professional</div>
            <div class="tier-price">$8.5<small>/1K tokens</small></div>
            <div class="tier-discount">15% OFF</div>
            <ul class="feature-list">
                <li><i class="fas fa-check"></i> Advanced LoRA training</li>
                <li><i class="fas fa-check"></i> Priority GPU access</li>
                <li><i class="fas fa-check"></i> Email support</li>
                <li><i class="fas fa-check"></i> Advanced sample generation</li>
                <li><i class="fas fa-check"></i> Custom model architectures</li>
                <li><i class="fas fa-check"></i> API access</li>
                <li><i class="fas fa-check"></i> Max 10K training steps</li>
                <li><i class="fas fa-check"></i> Max 1K images per dataset</li>
            </ul>
            <button class="btn btn-pricing" onclick="selectTier('professional')">Choose Professional</button>
        </div>

        <div class="pricing-card">
            <div class="tier-name">Enterprise</div>
            <div class="tier-price">$7<small>/1K tokens</small></div>
            <div class="tier-discount">30% OFF</div>
            <ul class="feature-list">
                <li><i class="fas fa-check"></i> Unlimited LoRA training</li>
                <li><i class="fas fa-check"></i> Dedicated GPU clusters</li>
                <li><i class="fas fa-check"></i> 24/7 priority support</li>
                <li><i class="fas fa-check"></i> Custom sample generation</li>
                <li><i class="fas fa-check"></i> White-label solutions</li>
                <li><i class="fas fa-check"></i> Advanced API access</li>
                <li><i class="fas fa-check"></i> Custom integrations</li>
                <li><i class="fas fa-check"></i> SLA guarantees</li>
            </ul>
            <button class="btn btn-pricing" onclick="selectTier('enterprise')">Contact Sales</button>
        </div>
    </section>

    <!-- Token Packages -->
    <section class="token-packages">
        <div class="container">
            <h2 class="text-center mb-4">Token Packages</h2>
            <p class="text-center text-secondary mb-4">Purchase tokens to power your AI training projects</p>
            
            <div class="package-grid" id="token-packages">
                <!-- Packages loaded dynamically -->
            </div>
        </div>
    </section>

    <!-- Cost Calculator -->
    <section class="cost-calculator">
        <div class="container">
            <h2 class="text-center mb-4">Training Cost Calculator</h2>
            <p class="text-center text-secondary mb-4">Estimate the cost of your LoRA training project</p>
            
            <div class="calculator-form">
                <div>
                    <label class="form-label">Training Steps</label>
                    <input type="number" class="form-control" id="calc-steps" value="1000" min="100" max="10000">
                </div>
                <div>
                    <label class="form-label">Number of Images</label>
                    <input type="number" class="form-control" id="calc-images" value="50" min="1" max="1000">
                </div>
                <div>
                    <label class="form-label">Rank</label>
                    <input type="number" class="form-control" id="calc-rank" value="16" min="4" max="128">
                </div>
                <div>
                    <label class="form-label">Model Type</label>
                    <select class="form-control" id="calc-model">
                        <option value="flux">FLUX.1 Dev</option>
                        <option value="schnell">FLUX.1 Schnell</option>
                        <option value="hidream">HiDream</option>
                        <option value="wan21_1b">WAN 2.1 1B</option>
                        <option value="wan21_14b">WAN 2.1 14B</option>
                    </select>
                </div>
            </div>
            
            <div class="text-center mb-3">
                <button class="btn btn-pricing" onclick="calculateCost()">Calculate Cost</button>
            </div>
            
            <div class="cost-result" id="cost-result" style="display: none;">
                <div class="cost-amount" id="total-cost">0 tokens</div>
                <div class="text-secondary">Estimated training cost</div>
                <div class="cost-breakdown" id="cost-breakdown">
                    <!-- Breakdown loaded dynamically -->
                </div>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let authToken = localStorage.getItem('authToken');
        let userInfo = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            if (authToken) {
                loadUserProfile();
            }
            loadTokenPackages();
        });

        // Authentication functions
        function showLogin() {
            const email = prompt('Email:');
            const password = prompt('Password:');

            if (email && password) {
                fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        authToken = data.token;
                        localStorage.setItem('authToken', authToken);
                        userInfo = data.user;
                        updateAuthUI();
                        showNotification('Login successful!', 'success');
                    } else {
                        showNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    showNotification('Login failed: ' + error.message, 'error');
                });
            }
        }

        function showRegister() {
            const email = prompt('Email:');
            const password = prompt('Password:');
            const tier = prompt('Tier (starter/professional/enterprise):', 'starter');

            if (email && password) {
                fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password, tier })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Registration successful! Please login.', 'success');
                    } else {
                        showNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    showNotification('Registration failed: ' + error.message, 'error');
                });
            }
        }

        function logout() {
            authToken = null;
            userInfo = null;
            localStorage.removeItem('authToken');
            updateAuthUI();
            showNotification('Logged out successfully', 'info');
        }

        function loadUserProfile() {
            fetch('/api/user/profile', {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.user) {
                    userInfo = data.user;
                    document.getElementById('token-balance').textContent = data.token_balance;
                    updateAuthUI();
                }
            })
            .catch(error => {
                console.error('Failed to load profile:', error);
                logout();
            });
        }

        function updateAuthUI() {
            const authButtons = document.getElementById('auth-buttons');
            const userInfoDiv = document.getElementById('user-info');

            if (authToken && userInfo) {
                authButtons.style.display = 'none';
                userInfoDiv.style.display = 'block';
            } else {
                authButtons.style.display = 'block';
                userInfoDiv.style.display = 'none';
            }
        }

        // Load token packages
        function loadTokenPackages() {
            fetch('/api/tokens/packages')
                .then(response => response.json())
                .then(data => {
                    if (data.packages) {
                        displayTokenPackages(data.packages);
                    }
                })
                .catch(error => {
                    console.error('Failed to load packages:', error);
                });
        }

        function displayTokenPackages(packages) {
            const container = document.getElementById('token-packages');
            container.innerHTML = '';

            Object.entries(packages).forEach(([key, pkg]) => {
                const isPopular = pkg.popular;
                const totalTokens = pkg.tokens + pkg.bonus;
                const savings = pkg.bonus > 0 ? Math.round((pkg.bonus / pkg.tokens) * 100) : 0;

                container.innerHTML += `
                    <div class="package-card ${isPopular ? 'popular' : ''}">
                        ${isPopular ? '<div class="badge bg-success mb-2">POPULAR</div>' : ''}
                        <h4>${totalTokens.toLocaleString()}</h4>
                        <p class="text-muted">tokens</p>
                        <div class="h3 text-primary">$${pkg.price}</div>
                        ${savings > 0 ? `<div class="badge bg-success mb-2">${savings}% Bonus</div>` : ''}
                        <button class="btn btn-pricing btn-sm" onclick="purchasePackage('${key}')">
                            Purchase
                        </button>
                    </div>
                `;
            });
        }

        function purchasePackage(packageName) {
            if (!authToken) {
                showNotification('Please login to purchase tokens', 'warning');
                return;
            }

            fetch('/api/tokens/purchase', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify({
                    package_name: packageName,
                    payment_method: 'stripe'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(`Successfully purchased ${data.tokens_added} tokens!`, 'success');
                    loadUserProfile(); // Refresh balance
                } else {
                    showNotification(data.error, 'error');
                }
            })
            .catch(error => {
                showNotification('Purchase failed: ' + error.message, 'error');
            });
        }

        // Cost calculator
        function calculateCost() {
            if (!authToken) {
                showNotification('Please login to calculate costs', 'warning');
                return;
            }

            const steps = parseInt(document.getElementById('calc-steps').value);
            const images = parseInt(document.getElementById('calc-images').value);
            const rank = parseInt(document.getElementById('calc-rank').value);
            const modelType = document.getElementById('calc-model').value;

            fetch('/api/pricing/calculate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify({
                    steps,
                    num_images: images,
                    rank,
                    model_type: modelType,
                    batch_size: 1
                })
            })
            .then(response => response.json())
            .then(data => {
                displayCostResult(data);
            })
            .catch(error => {
                showNotification('Cost calculation failed: ' + error.message, 'error');
            });
        }

        function displayCostResult(costData) {
            document.getElementById('total-cost').textContent = `${costData.total_tokens.toLocaleString()} tokens`;

            const breakdown = document.getElementById('cost-breakdown');
            breakdown.innerHTML = `
                <div class="breakdown-item">
                    <div class="h6">Compute</div>
                    <div class="text-muted">${costData.breakdown.base_compute} tokens</div>
                </div>
                <div class="breakdown-item">
                    <div class="h6">Data Processing</div>
                    <div class="text-muted">${costData.breakdown.data_processing} tokens</div>
                </div>
                <div class="breakdown-item">
                    <div class="h6">Storage</div>
                    <div class="text-muted">${costData.breakdown.storage} tokens</div>
                </div>
                <div class="breakdown-item">
                    <div class="h6">Infrastructure</div>
                    <div class="text-muted">${costData.breakdown.infrastructure} tokens</div>
                </div>
                <div class="breakdown-item">
                    <div class="h6">Estimated Time</div>
                    <div class="text-muted">${costData.estimated_time}</div>
                </div>
                <div class="breakdown-item">
                    <div class="h6">GPU Type</div>
                    <div class="text-muted">${costData.gpu_type}</div>
                </div>
            `;

            document.getElementById('cost-result').style.display = 'block';
        }

        function selectTier(tier) {
            if (tier === 'enterprise') {
                showNotification('Please contact sales for Enterprise pricing', 'info');
                return;
            }

            showNotification(`Selected ${tier} tier. Please register or login to continue.`, 'info');
        }

        function showNotification(message, type = 'info') {
            const colors = {
                success: '#00ff88',
                error: '#ff4757',
                warning: '#ffa502',
                info: '#00d4ff'
            };

            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: ${colors[type]};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                z-index: 9999;
                max-width: 300px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
            `;

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
    </script>

    <style>
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
{% endblock %}

{% block extra_js %}
<script>
    // Additional pricing-specific JavaScript can go here
</script>
{% endblock %}
