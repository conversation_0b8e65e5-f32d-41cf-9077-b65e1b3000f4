<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Toolkit - LoRA Training Studio</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark-bg: #1a1a2e;
            --card-bg: rgba(255, 255, 255, 0.95);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-radius: 20px;
            --shadow-soft: 0 10px 40px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Animated Background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: var(--primary-gradient);
        }

        .bg-animation::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }

        /* Header */
        .header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header h1 {
            color: white;
            font-weight: 700;
            font-size: 2rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        /* Main Layout */
        .main-container {
            display: grid;
            grid-template-columns: 350px 1fr 400px;
            grid-template-rows: auto 1fr;
            gap: 2rem;
            padding: 2rem;
            min-height: calc(100vh - 100px);
            max-width: 1800px;
            margin: 0 auto;
        }

        /* Model Selector - Vertical Slider */
        .model-selector {
            grid-row: 1 / -1;
            background: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow-soft);
            backdrop-filter: blur(20px);
            height: fit-content;
            max-height: calc(100vh - 140px);
            overflow-y: auto;
            position: sticky;
            top: 120px;
        }

        .model-selector h3 {
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            font-weight: 600;
        }

        .model-slider {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .model-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .model-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary-gradient);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .model-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
            border-color: #667eea;
        }

        .model-card:hover::before {
            opacity: 0.1;
        }

        .model-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #f8faff 0%, #e8f2ff 100%);
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
        }

        .model-card-content {
            position: relative;
            z-index: 2;
        }

        .model-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .model-desc {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.75rem;
        }

        .model-specs {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .spec-badge {
            background: #e2e8f0;
            color: #4a5568;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .spec-badge.recommended {
            background: var(--success-gradient);
            color: white;
        }

        /* Upload Section */
        .upload-section {
            background: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow-soft);
            backdrop-filter: blur(20px);
        }

        .upload-area {
            border: 3px dashed #cbd5e0;
            border-radius: 15px;
            padding: 3rem 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            position: relative;
            overflow: hidden;
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: linear-gradient(135deg, #e8f2ff 0%, #f0f8ff 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
        }

        .upload-area:hover::before {
            left: 100%;
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        /* Image Grid */
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .image-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            group: hover;
        }

        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .image-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .image-card:hover img {
            transform: scale(1.05);
        }

        .image-info {
            padding: 1rem;
        }

        .remove-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(239, 68, 68, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .image-card:hover .remove-btn {
            opacity: 1;
        }

        .remove-btn:hover {
            background: #dc2626;
            transform: scale(1.1);
        }

        /* Control Panel */
        .control-panel {
            background: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow-soft);
            backdrop-filter: blur(20px);
            height: fit-content;
            position: sticky;
            top: 120px;
        }

        /* Responsive Design */
        @media (max-width: 1400px) {
            .main-container {
                grid-template-columns: 300px 1fr 350px;
            }
        }

        @media (max-width: 1200px) {
            .main-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto;
                gap: 1.5rem;
            }
            
            .model-selector,
            .control-panel {
                position: static;
                max-height: none;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
                gap: 1rem;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
            
            .model-selector,
            .upload-section,
            .control-panel {
                padding: 1.5rem;
            }
        }

        /* Animations */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in-left {
            animation: slideInLeft 0.6s ease-out;
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .slide-in-right {
            animation: slideInRight 0.6s ease-out;
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.5);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.7);
        }
    </style>
</head>
<body>
    <div class="bg-animation"></div>
    
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <div class="d-flex align-items-center justify-content-between">
                <h1><i class="fas fa-robot me-3"></i>LoRA Training Studio</h1>
                <div class="d-flex align-items-center gap-3">
                    <div class="status-indicator" id="training-status">
                        <i class="fas fa-circle text-success"></i>
                        <span class="text-white">Ready</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Layout -->
    <div class="main-container">
        <!-- Model Selector -->
        <div class="model-selector slide-in-left">
            <h3><i class="fas fa-cog me-2"></i>Choose Model</h3>
            <div class="model-slider" id="model-slider">
                <!-- Models will be loaded here -->
            </div>
        </div>

        <!-- Upload Section -->
        <div class="upload-section fade-in">
            <h3><i class="fas fa-upload me-2"></i>Upload Training Images</h3>
            
            <div class="upload-area" id="upload-area">
                <div class="upload-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <h4>Drag & Drop Images Here</h4>
                <p class="text-muted mb-3">or click to browse files</p>
                <button class="btn btn-primary btn-lg">
                    <i class="fas fa-folder-open me-2"></i>Browse Files
                </button>
                <input type="file" id="file-input" multiple accept="image/*" style="display: none;">
            </div>

            <!-- Image Grid -->
            <div id="image-grid-container" style="display: none;">
                <div class="d-flex justify-content-between align-items-center mt-4 mb-3">
                    <h5>Uploaded Images (<span id="image-count">0</span>)</h5>
                    <button class="btn btn-outline-danger btn-sm" onclick="clearAllImages()">
                        <i class="fas fa-trash me-2"></i>Clear All
                    </button>
                </div>
                <div class="image-grid" id="image-grid"></div>
            </div>

            <!-- Caption Section -->
            <div id="caption-section" style="display: none;" class="mt-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-comment-alt me-2"></i>Auto Caption</h5>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="generateCaptions()">
                            <i class="fas fa-magic me-2"></i>Generate with BLIP-2
                        </button>
                        <button class="btn btn-outline-secondary" onclick="showManualCaptions()">
                            <i class="fas fa-edit me-2"></i>Manual Edit
                        </button>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Concept/Trigger Word (optional)</label>
                    <input type="text" class="form-control" id="concept-sentence" 
                           placeholder="e.g., 'a photo of [trigger]'">
                </div>

                <div id="caption-progress" style="display: none;" class="mb-3">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             style="width: 0%"></div>
                    </div>
                    <small class="text-muted">Generating captions...</small>
                </div>

                <div id="caption-editor" style="display: none;"></div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="control-panel slide-in-right">
            <h3><i class="fas fa-sliders-h me-2"></i>Training Configuration</h3>
            
            <!-- Quick Presets -->
            <div class="mb-4">
                <label class="form-label">Quick Presets</label>
                <div class="btn-group w-100" role="group">
                    <input type="radio" class="btn-check" name="preset" id="preset-fast" checked>
                    <label class="btn btn-outline-primary" for="preset-fast">Fast</label>
                    
                    <input type="radio" class="btn-check" name="preset" id="preset-balanced">
                    <label class="btn btn-outline-primary" for="preset-balanced">Balanced</label>
                    
                    <input type="radio" class="btn-check" name="preset" id="preset-quality">
                    <label class="btn btn-outline-primary" for="preset-quality">Quality</label>
                </div>
            </div>

            <!-- LoRA Name -->
            <div class="mb-3">
                <label class="form-label">LoRA Name</label>
                <input type="text" class="form-control" id="lora-name" 
                       placeholder="my-custom-lora">
            </div>

            <!-- Training Steps -->
            <div class="mb-3">
                <label class="form-label">Training Steps</label>
                <div class="d-flex align-items-center gap-2">
                    <input type="range" class="form-range" id="steps-range" 
                           min="100" max="5000" value="1000" step="100">
                    <input type="number" class="form-control" id="steps" 
                           style="width: 100px;" value="1000" min="100" max="5000">
                </div>
            </div>

            <!-- Learning Rate -->
            <div class="mb-3">
                <label class="form-label">Learning Rate</label>
                <div class="d-flex align-items-center gap-2">
                    <input type="range" class="form-range" id="lr-range" 
                           min="0.0001" max="0.01" value="0.001" step="0.0001">
                    <input type="number" class="form-control" id="learning-rate" 
                           style="width: 120px;" value="0.001" step="0.0001">
                </div>
            </div>

            <!-- Advanced Settings Toggle -->
            <div class="mb-3">
                <button class="btn btn-outline-secondary w-100" type="button" 
                        data-bs-toggle="collapse" data-bs-target="#advanced-settings">
                    <i class="fas fa-cogs me-2"></i>Advanced Settings
                </button>
            </div>

            <div class="collapse" id="advanced-settings">
                <!-- Rank -->
                <div class="mb-3">
                    <label class="form-label">Rank</label>
                    <input type="number" class="form-control" id="rank" value="16" min="4" max="128">
                </div>

                <!-- Batch Size -->
                <div class="mb-3">
                    <label class="form-label">Batch Size</label>
                    <input type="number" class="form-control" id="batch-size" value="1" min="1" max="8">
                </div>

                <!-- Alpha -->
                <div class="mb-3">
                    <label class="form-label">Alpha</label>
                    <input type="number" class="form-control" id="alpha" value="16" min="1" max="128">
                </div>
            </div>

            <!-- Training Summary -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title">Training Summary</h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="text-primary fw-bold" id="summary-images">0</div>
                            <small class="text-muted">Images</small>
                        </div>
                        <div class="col-6">
                            <div class="text-success fw-bold" id="summary-time">-</div>
                            <small class="text-muted">Est. Time</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Start Training Button -->
            <button class="btn btn-success btn-lg w-100 mt-4" id="start-training-btn" 
                    onclick="startTraining()" disabled>
                <i class="fas fa-rocket me-2"></i>Start Training
            </button>

            <!-- Training Progress -->
            <div id="training-progress" style="display: none;" class="mt-4">
                <div class="progress mb-2">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         id="training-progress-bar" style="width: 0%"></div>
                </div>
                <div class="d-flex justify-content-between">
                    <small class="text-muted">Training in progress...</small>
                    <button class="btn btn-outline-danger btn-sm" onclick="stopTraining()">
                        <i class="fas fa-stop me-1"></i>Stop
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let selectedModel = null;
        let currentSessionId = null;
        let uploadedFiles = [];
        let captions = {};

        // Model configurations
        const MODELS = [
            {
                key: 'modal_train_lora_flux_24gb',
                name: 'FLUX.1 Dev',
                description: 'High-quality image generation with excellent detail',
                vram: '24GB',
                recommended: true,
                specs: ['flux', 'high-quality', '24gb']
            },
            {
                key: 'modal_train_lora_flux_schnell_24gb',
                name: 'FLUX.1 Schnell',
                description: 'Fast generation with good quality balance',
                vram: '24GB',
                recommended: false,
                specs: ['flux', 'fast', '24gb']
            },
            {
                key: 'train_lora_hidream_48',
                name: 'HiDream',
                description: 'High-resolution dream-like generation',
                vram: '48GB',
                recommended: false,
                specs: ['hidream', 'high-res', '48gb']
            },
            {
                key: 'train_lora_wan21_1b_24gb',
                name: 'WAN 2.1 1B',
                description: 'Efficient 1B parameter model for quick training',
                vram: '24GB',
                recommended: false,
                specs: ['wan21', 'efficient', '24gb']
            },
            {
                key: 'train_lora_wan21_14b_24gb',
                name: 'WAN 2.1 14B',
                description: 'Large 14B parameter model for maximum quality',
                vram: '24GB',
                recommended: false,
                specs: ['wan21', 'large', '24gb']
            }
        ];

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Modern UI initializing...');

            loadModels();
            setupFileUpload();
            setupPresets();
            setupRangeInputs();

            // Auto-select recommended model
            const recommendedModel = MODELS.find(m => m.recommended);
            if (recommendedModel) {
                selectModel(recommendedModel.key);
            }

            console.log('✅ Modern UI initialized');
        });

        function loadModels() {
            const container = document.getElementById('model-slider');

            let html = '';
            MODELS.forEach(model => {
                html += `
                    <div class="model-card ${model.recommended ? 'selected' : ''}"
                         onclick="selectModel('${model.key}')"
                         data-model="${model.key}">
                        <div class="model-card-content">
                            <div class="model-name">${model.name}</div>
                            <div class="model-desc">${model.description}</div>
                            <div class="model-specs">
                                ${model.specs.map(spec =>
                                    `<span class="spec-badge ${model.recommended && spec === 'high-quality' ? 'recommended' : ''}">${spec}</span>`
                                ).join('')}
                                ${model.recommended ? '<span class="spec-badge recommended">⭐ recommended</span>' : ''}
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function selectModel(modelKey) {
            console.log('🎯 Model selected:', modelKey);
            selectedModel = modelKey;

            // Update visual selection
            document.querySelectorAll('.model-card').forEach(card => {
                card.classList.remove('selected');
            });

            const selectedCard = document.querySelector(`[data-model="${modelKey}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');

                // Smooth scroll to selected model
                selectedCard.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest'
                });
            }

            updateTrainingSummary();
            showNotification(`Selected: ${MODELS.find(m => m.key === modelKey)?.name}`, 'success');
        }

        function setupFileUpload() {
            const uploadArea = document.getElementById('upload-area');
            const fileInput = document.getElementById('file-input');

            uploadArea.addEventListener('click', () => fileInput.click());

            fileInput.addEventListener('change', function(e) {
                handleFileUpload(e.target.files);
            });

            // Drag and drop
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#667eea';
                uploadArea.style.transform = 'translateY(-5px)';
            });

            uploadArea.addEventListener('dragleave', function() {
                uploadArea.style.borderColor = '#cbd5e0';
                uploadArea.style.transform = 'translateY(0)';
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#cbd5e0';
                uploadArea.style.transform = 'translateY(0)';
                handleFileUpload(e.dataTransfer.files);
            });
        }

        function handleFileUpload(files) {
            console.log('📤 Uploading files:', files.length);

            const formData = new FormData();
            let imageCount = 0;

            Array.from(files).forEach(file => {
                if (file.type.startsWith('image/')) {
                    formData.append('files', file);
                    imageCount++;
                }
            });

            if (imageCount === 0) {
                showNotification('Please select image files (JPG, PNG, GIF)', 'warning');
                return;
            }

            showNotification(`Uploading ${imageCount} images...`, 'info');

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showNotification(data.error, 'danger');
                    return;
                }

                currentSessionId = data.session_id;
                uploadedFiles = data.files;

                displayImages();
                showCaptionSection();
                updateTrainingSummary();

                showNotification(`Successfully uploaded ${data.files.length} images`, 'success');
            })
            .catch(error => {
                console.error('Upload error:', error);
                showNotification('Upload failed', 'danger');
            });
        }

        function displayImages() {
            const container = document.getElementById('image-grid-container');
            const grid = document.getElementById('image-grid');
            const count = document.getElementById('image-count');

            if (uploadedFiles.length === 0) {
                container.style.display = 'none';
                return;
            }

            container.style.display = 'block';
            count.textContent = uploadedFiles.length;

            let html = '';
            uploadedFiles.forEach((file, index) => {
                html += `
                    <div class="image-card fade-in" style="animation-delay: ${index * 0.1}s">
                        <img src="/uploads/${currentSessionId}/${file.filename}" alt="${file.filename}">
                        <button class="remove-btn" onclick="removeImage(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="image-info">
                            <h6 class="mb-1">${file.filename}</h6>
                            <small class="text-muted">${formatFileSize(file.size)}</small>
                            ${file.width && file.height ? `<br><small class="text-muted">${file.width}×${file.height}</small>` : ''}
                        </div>
                    </div>
                `;
            });

            grid.innerHTML = html;
        }

        function removeImage(index) {
            uploadedFiles.splice(index, 1);
            delete captions[Object.keys(captions)[index]];

            displayImages();
            updateTrainingSummary();

            if (uploadedFiles.length === 0) {
                document.getElementById('caption-section').style.display = 'none';
            }
        }

        function clearAllImages() {
            if (confirm('Remove all images?')) {
                uploadedFiles = [];
                captions = {};
                displayImages();
                updateTrainingSummary();
                document.getElementById('caption-section').style.display = 'none';
            }
        }

        function showCaptionSection() {
            const section = document.getElementById('caption-section');
            section.style.display = 'block';
            section.classList.add('fade-in');
        }

        function generateCaptions() {
            if (!currentSessionId || uploadedFiles.length === 0) {
                showNotification('Please upload images first', 'warning');
                return;
            }

            const conceptSentence = document.getElementById('concept-sentence').value;
            const progressDiv = document.getElementById('caption-progress');
            const progressBar = progressDiv.querySelector('.progress-bar');

            progressDiv.style.display = 'block';
            progressBar.style.width = '0%';

            showNotification('Generating captions with BLIP-2...', 'info');

            fetch('/api/caption', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: currentSessionId,
                    concept_sentence: conceptSentence
                })
            })
            .then(response => response.json())
            .then(data => {
                progressBar.style.width = '100%';

                if (data.error) {
                    showNotification(data.error, 'danger');
                    return;
                }

                captions = data.captions;
                showCaptionEditor();
                showNotification(data.message, 'success');

                setTimeout(() => {
                    progressDiv.style.display = 'none';
                }, 2000);
            })
            .catch(error => {
                console.error('Captioning error:', error);
                showNotification('Captioning failed', 'danger');
                progressDiv.style.display = 'none';
            });
        }

        function showManualCaptions() {
            if (!currentSessionId || uploadedFiles.length === 0) {
                showNotification('Please upload images first', 'warning');
                return;
            }

            // Initialize empty captions
            uploadedFiles.forEach(file => {
                if (!captions[file.filename]) {
                    captions[file.filename] = '';
                }
            });

            showCaptionEditor();
            showNotification('You can now manually edit captions', 'info');
        }

        function showCaptionEditor() {
            const editor = document.getElementById('caption-editor');

            let html = '<div class="row">';
            uploadedFiles.forEach((file, index) => {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="row g-0">
                                <div class="col-4">
                                    <img src="/uploads/${currentSessionId}/${file.filename}"
                                         class="img-fluid h-100" style="object-fit: cover;" alt="${file.filename}">
                                </div>
                                <div class="col-8">
                                    <div class="card-body">
                                        <h6 class="card-title">${file.filename}</h6>
                                        <textarea class="form-control caption-input"
                                                  data-filename="${file.filename}"
                                                  placeholder="Enter caption..."
                                                  rows="3">${captions[file.filename] || ''}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';

            editor.innerHTML = html;
            editor.style.display = 'block';

            // Add event listeners
            document.querySelectorAll('.caption-input').forEach(input => {
                input.addEventListener('input', (e) => {
                    const filename = e.target.dataset.filename;
                    captions[filename] = e.target.value;
                });
            });
        }

        function setupPresets() {
            const presets = {
                fast: { steps: 500, lr: 0.002, rank: 8, batchSize: 2 },
                balanced: { steps: 1000, lr: 0.001, rank: 16, batchSize: 1 },
                quality: { steps: 2000, lr: 0.0005, rank: 32, batchSize: 1 }
            };

            document.querySelectorAll('input[name="preset"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.checked) {
                        const preset = presets[this.id.replace('preset-', '')];
                        document.getElementById('steps').value = preset.steps;
                        document.getElementById('steps-range').value = preset.steps;
                        document.getElementById('learning-rate').value = preset.lr;
                        document.getElementById('lr-range').value = preset.lr;
                        document.getElementById('rank').value = preset.rank;
                        document.getElementById('batch-size').value = preset.batchSize;
                        updateTrainingSummary();
                    }
                });
            });
        }

        function setupRangeInputs() {
            // Sync range and number inputs
            const pairs = [
                ['steps-range', 'steps'],
                ['lr-range', 'learning-rate']
            ];

            pairs.forEach(([rangeId, numberId]) => {
                const range = document.getElementById(rangeId);
                const number = document.getElementById(numberId);

                range.addEventListener('input', () => {
                    number.value = range.value;
                    updateTrainingSummary();
                });

                number.addEventListener('input', () => {
                    range.value = number.value;
                    updateTrainingSummary();
                });
            });

            // Update summary on other input changes
            ['lora-name', 'rank', 'batch-size', 'alpha'].forEach(id => {
                document.getElementById(id).addEventListener('input', updateTrainingSummary);
            });
        }

        function updateTrainingSummary() {
            const summaryImages = document.getElementById('summary-images');
            const summaryTime = document.getElementById('summary-time');
            const startBtn = document.getElementById('start-training-btn');
            const loraName = document.getElementById('lora-name').value;
            const steps = parseInt(document.getElementById('steps').value);

            // Update summary
            summaryImages.textContent = uploadedFiles.length;

            // Estimate training time
            if (uploadedFiles.length > 0 && steps) {
                const timePerStep = 2; // seconds per step
                const totalMinutes = Math.round((steps * timePerStep) / 60);
                if (totalMinutes < 60) {
                    summaryTime.textContent = `${totalMinutes}m`;
                } else {
                    const hours = Math.floor(totalMinutes / 60);
                    const minutes = totalMinutes % 60;
                    summaryTime.textContent = `${hours}h ${minutes}m`;
                }
            } else {
                summaryTime.textContent = '-';
            }

            // Enable/disable start button
            const canStart = selectedModel && uploadedFiles.length > 0 && loraName.trim();
            startBtn.disabled = !canStart;
        }

        function startTraining() {
            if (!selectedModel || !currentSessionId || uploadedFiles.length === 0) {
                showNotification('Please complete all steps first', 'warning');
                return;
            }

            const loraName = document.getElementById('lora-name').value.trim();
            if (!loraName) {
                showNotification('Please enter a LoRA name', 'warning');
                return;
            }

            const trainingData = {
                session_id: currentSessionId,
                config_name: selectedModel,
                lora_name: loraName,
                concept_sentence: document.getElementById('concept-sentence').value,
                captions: captions,
                steps: parseInt(document.getElementById('steps').value),
                learning_rate: parseFloat(document.getElementById('learning-rate').value),
                rank: parseInt(document.getElementById('rank').value),
                batch_size: parseInt(document.getElementById('batch-size').value),
                alpha: parseInt(document.getElementById('alpha').value)
            };

            document.getElementById('start-training-btn').style.display = 'none';
            document.getElementById('training-progress').style.display = 'block';

            fetch('/api/train', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(trainingData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showNotification(data.error, 'danger');
                    resetTrainingUI();
                    return;
                }

                showNotification(data.message, 'success');
                startProgressPolling();
            })
            .catch(error => {
                console.error('Training error:', error);
                showNotification('Failed to start training', 'danger');
                resetTrainingUI();
            });
        }

        function stopTraining() {
            fetch('/api/training/stop', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                showNotification(data.message, 'info');
                resetTrainingUI();
            })
            .catch(error => {
                console.error('Stop training error:', error);
                showNotification('Failed to stop training', 'danger');
            });
        }

        function resetTrainingUI() {
            document.getElementById('start-training-btn').style.display = 'block';
            document.getElementById('training-progress').style.display = 'none';
        }

        function startProgressPolling() {
            const progressBar = document.getElementById('training-progress-bar');

            const pollInterval = setInterval(() => {
                fetch('/api/training/status')
                    .then(response => response.json())
                    .then(data => {
                        if (!data.is_training) {
                            clearInterval(pollInterval);
                            resetTrainingUI();

                            if (data.error) {
                                showNotification('Training failed: ' + data.error, 'danger');
                            } else if (data.progress === 100) {
                                showNotification('Training completed successfully!', 'success');
                            }
                        } else {
                            progressBar.style.width = data.progress + '%';
                            progressBar.textContent = data.progress + '%';
                        }
                    })
                    .catch(error => {
                        console.error('Progress polling error:', error);
                    });
            }, 2000);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                border: none;
                border-radius: 15px;
            `;

            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
