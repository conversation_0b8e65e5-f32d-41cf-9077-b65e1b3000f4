<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Toolkit - LoRA Training Studio</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Advanced Color Palette */
            --primary-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            --accent-gradient: linear-gradient(135deg, #00d4ff 0%, #090979 50%, #020024 100%);
            --success-gradient: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            --warning-gradient: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            --danger-gradient: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

            /* Dark Theme Colors */
            --bg-primary: #0a0a0f;
            --bg-secondary: #1a1a2e;
            --bg-tertiary: #16213e;
            --surface-primary: rgba(26, 26, 46, 0.95);
            --surface-secondary: rgba(22, 33, 62, 0.9);
            --surface-glass: rgba(255, 255, 255, 0.05);
            --surface-glass-hover: rgba(255, 255, 255, 0.1);

            /* Text Colors */
            --text-primary: #ffffff;
            --text-secondary: #b8c5d1;
            --text-muted: #6b7280;
            --text-accent: #00d4ff;

            /* Neon Accents */
            --neon-blue: #00d4ff;
            --neon-purple: #8b5cf6;
            --neon-green: #00ff88;
            --neon-pink: #ff6b9d;
            --neon-orange: #ff6b35;

            /* Advanced Shadows */
            --shadow-neon: 0 0 20px rgba(0, 212, 255, 0.3);
            --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-medium: 0 16px 48px rgba(0, 0, 0, 0.4);
            --shadow-hard: 0 24px 64px rgba(0, 0, 0, 0.5);
            --shadow-glow: 0 0 40px rgba(0, 212, 255, 0.2);

            /* Border Radius */
            --radius-sm: 8px;
            --radius-md: 16px;
            --radius-lg: 24px;
            --radius-xl: 32px;

            /* Animations */
            --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-primary);
            min-height: 100vh;
            overflow-x: hidden;
            color: var(--text-primary);
        }

        /* Advanced Animated Background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: var(--primary-gradient);
            overflow: hidden;
        }

        .bg-animation::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 255, 136, 0.05) 0%, transparent 50%);
            animation: float 30s ease-in-out infinite;
        }

        .bg-animation::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                linear-gradient(45deg, transparent 30%, rgba(0, 212, 255, 0.02) 50%, transparent 70%),
                linear-gradient(-45deg, transparent 30%, rgba(139, 92, 246, 0.02) 50%, transparent 70%);
            animation: pulse 20s ease-in-out infinite alternate;
        }

        @keyframes float {
            0%, 100% { transform: translateX(0px) translateY(0px) rotate(0deg); }
            33% { transform: translateX(30px) translateY(-30px) rotate(1deg); }
            66% { transform: translateX(-20px) translateY(20px) rotate(-1deg); }
        }

        @keyframes pulse {
            0% { opacity: 0.3; }
            100% { opacity: 0.8; }
        }

        /* Advanced Header */
        .header {
            background: var(--surface-glass);
            backdrop-filter: blur(24px) saturate(180%);
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            padding: 1.5rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow-soft);
        }

        .header h1 {
            color: var(--text-primary);
            font-weight: 700;
            font-size: 2.2rem;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .status-indicator {
            background: var(--surface-secondary);
            padding: 0.5rem 1rem;
            border-radius: var(--radius-lg);
            border: 1px solid var(--neon-blue);
            box-shadow: var(--shadow-neon);
        }

        .status-indicator i {
            color: var(--neon-green);
            filter: drop-shadow(0 0 8px var(--neon-green));
        }

        /* Main Layout */
        .main-container {
            display: grid;
            grid-template-columns: 350px 1fr 400px;
            grid-template-rows: auto 1fr;
            gap: 2rem;
            padding: 2rem;
            min-height: calc(100vh - 100px);
            max-width: 1800px;
            margin: 0 auto;
        }

        /* Advanced Model Selector */
        .model-selector {
            grid-row: 1 / -1;
            background: var(--surface-primary);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-medium);
            backdrop-filter: blur(24px) saturate(180%);
            border: 1px solid rgba(0, 212, 255, 0.1);
            height: fit-content;
            max-height: calc(100vh - 140px);
            overflow-y: auto;
            position: sticky;
            top: 120px;
        }

        .model-selector h3 {
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            font-weight: 600;
            font-size: 1.4rem;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .model-slider {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .model-card {
            background: var(--surface-secondary);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(16px);
        }

        .model-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
            opacity: 0;
            transition: var(--transition-normal);
            z-index: 1;
        }

        .model-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-glow);
            border-color: var(--neon-blue);
        }

        .model-card:hover::before {
            opacity: 0.05;
        }

        .model-card.selected {
            border-color: var(--neon-blue);
            background: var(--surface-secondary);
            transform: translateY(-5px);
            box-shadow:
                var(--shadow-neon),
                0 0 60px rgba(0, 212, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .model-card.selected::before {
            opacity: 0.1;
        }

        .model-card-content {
            position: relative;
            z-index: 2;
        }

        .model-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
            background: linear-gradient(135deg, var(--text-primary), var(--text-accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .model-desc {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.75rem;
            line-height: 1.5;
        }

        .model-specs {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .spec-badge {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-lg);
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(8px);
            transition: var(--transition-fast);
        }

        .spec-badge:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--neon-blue);
            color: var(--neon-blue);
        }

        .spec-badge.recommended {
            background: var(--success-gradient);
            color: white;
            border-color: var(--neon-green);
            box-shadow: 0 0 16px rgba(0, 255, 136, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 16px rgba(0, 255, 136, 0.3); }
            to { box-shadow: 0 0 24px rgba(0, 255, 136, 0.5); }
        }

        /* Advanced Upload Section */
        .upload-section {
            background: var(--surface-primary);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-medium);
            backdrop-filter: blur(24px) saturate(180%);
            border: 1px solid rgba(0, 212, 255, 0.1);
        }

        .upload-section h3 {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.4rem;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .upload-area {
            border: 2px dashed rgba(0, 212, 255, 0.3);
            border-radius: var(--radius-lg);
            padding: 3rem 2rem;
            text-align: center;
            transition: var(--transition-normal);
            cursor: pointer;
            background: var(--surface-secondary);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(16px);
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
            transition: var(--transition-slow);
        }

        .upload-area:hover {
            border-color: var(--neon-blue);
            background: var(--surface-secondary);
            transform: translateY(-4px);
            box-shadow: var(--shadow-glow);
        }

        .upload-area:hover::before {
            left: 100%;
        }

        .upload-icon {
            font-size: 3.5rem;
            color: var(--neon-blue);
            margin-bottom: 1rem;
            animation: float-icon 3s ease-in-out infinite;
            filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.5));
        }

        .upload-area h4 {
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .upload-area p {
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }

        @keyframes float-icon {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* Image Grid */
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .image-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            group: hover;
        }

        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .image-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .image-card:hover img {
            transform: scale(1.05);
        }

        .image-info {
            padding: 1rem;
        }

        .remove-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(239, 68, 68, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .image-card:hover .remove-btn {
            opacity: 1;
        }

        .remove-btn:hover {
            background: #dc2626;
            transform: scale(1.1);
        }

        /* Advanced Control Panel */
        .control-panel {
            background: var(--surface-primary);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-medium);
            backdrop-filter: blur(24px) saturate(180%);
            border: 1px solid rgba(0, 212, 255, 0.1);
            height: fit-content;
            position: sticky;
            top: 120px;
        }

        .control-panel h3 {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.4rem;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Advanced Form Controls */
        .form-label {
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .form-control {
            background: var(--surface-secondary);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            padding: 0.75rem 1rem;
            transition: var(--transition-normal);
        }

        .form-control:focus {
            background: var(--surface-secondary);
            border-color: var(--neon-blue);
            box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.2);
            color: var(--text-primary);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        .form-range {
            background: transparent;
        }

        .form-range::-webkit-slider-track {
            background: var(--surface-secondary);
            border-radius: var(--radius-sm);
            height: 6px;
        }

        .form-range::-webkit-slider-thumb {
            background: var(--neon-blue);
            border: none;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            width: 20px;
            height: 20px;
        }

        /* Advanced Buttons */
        .btn {
            border-radius: var(--radius-md);
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: var(--transition-normal);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--accent-gradient);
            color: white;
            box-shadow: var(--shadow-neon);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-glow);
        }

        .btn-success {
            background: var(--success-gradient);
            color: white;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px rgba(0, 255, 136, 0.5);
        }

        .btn-outline-primary {
            background: transparent;
            border: 1px solid var(--neon-blue);
            color: var(--neon-blue);
        }

        .btn-outline-primary:hover,
        .btn-outline-primary.active {
            background: var(--neon-blue);
            color: var(--bg-primary);
            box-shadow: var(--shadow-neon);
        }

        .btn-outline-secondary {
            background: transparent;
            border: 1px solid var(--text-secondary);
            color: var(--text-secondary);
        }

        .btn-outline-secondary:hover,
        .btn-outline-secondary.active {
            background: var(--text-secondary);
            color: var(--bg-primary);
        }

        /* Responsive Design */
        @media (max-width: 1400px) {
            .main-container {
                grid-template-columns: 300px 1fr 350px;
            }
        }

        @media (max-width: 1200px) {
            .main-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto;
                gap: 1.5rem;
            }
            
            .model-selector,
            .control-panel {
                position: static;
                max-height: none;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
                gap: 1rem;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
            
            .model-selector,
            .upload-section,
            .control-panel {
                padding: 1.5rem;
            }
        }

        /* Advanced Animations */
        .fade-in {
            animation: fadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
                filter: blur(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0);
            }
        }

        /* Manager Styles */
        .token-balance {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid var(--neon-blue);
            border-radius: 20px;
            padding: 0.5rem 1rem;
            color: var(--neon-blue);
            font-weight: 600;
            margin-right: 1rem;
        }

        .token-balance.manager {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid var(--neon-green);
            color: var(--neon-green);
        }

        .manager-badge {
            background: var(--success-gradient);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .unlimited-training-banner {
            background: var(--success-gradient);
            color: white;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: 600;
            display: none;
        }

        .slide-in-left {
            animation: slideInLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px) rotateY(-10deg);
                filter: blur(5px);
            }
            to {
                opacity: 1;
                transform: translateX(0) rotateY(0);
                filter: blur(0);
            }
        }

        .slide-in-right {
            animation: slideInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px) rotateY(10deg);
                filter: blur(5px);
            }
            to {
                opacity: 1;
                transform: translateX(0) rotateY(0);
                filter: blur(0);
            }
        }

        /* Neon Pulse Animation */
        .neon-pulse {
            animation: neonPulse 2s ease-in-out infinite alternate;
        }

        @keyframes neonPulse {
            from {
                box-shadow:
                    0 0 20px rgba(0, 212, 255, 0.3),
                    0 0 40px rgba(0, 212, 255, 0.2),
                    inset 0 0 20px rgba(0, 212, 255, 0.1);
            }
            to {
                box-shadow:
                    0 0 30px rgba(0, 212, 255, 0.5),
                    0 0 60px rgba(0, 212, 255, 0.3),
                    inset 0 0 30px rgba(0, 212, 255, 0.2);
            }
        }

        /* Advanced Caption Editor */
        .caption-editor-card {
            background: var(--surface-secondary);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-soft);
            transition: var(--transition-normal);
            margin-bottom: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(16px);
        }

        .caption-editor-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-glow);
            border-color: var(--neon-blue);
        }

        .caption-image {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: var(--radius-md);
            border: 2px solid rgba(0, 212, 255, 0.2);
            transition: var(--transition-normal);
        }

        .caption-image:hover {
            border-color: var(--neon-blue);
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }

        .caption-input {
            background: var(--surface-primary);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            transition: var(--transition-normal);
            resize: vertical;
            min-height: 80px;
        }

        .caption-input:focus {
            border-color: var(--neon-blue);
            box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.2);
            background: var(--surface-primary);
        }

        .caption-input::placeholder {
            color: var(--text-muted);
        }

        /* Advanced Training Progress Monitor */
        .progress-monitor {
            background: var(--surface-primary);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-hard);
            backdrop-filter: blur(24px) saturate(180%);
            border: 1px solid rgba(0, 212, 255, 0.2);
            margin-top: 2rem;
        }

        .progress-monitor h3 {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.6rem;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .progress-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--surface-secondary);
            padding: 1.5rem;
            border-radius: var(--radius-lg);
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(16px);
            transition: var(--transition-normal);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-glow);
            border-color: var(--neon-blue);
        }

        .stat-value {
            font-size: 2.2rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .progress-log {
            background: var(--bg-primary);
            color: var(--neon-green);
            padding: 1.5rem;
            border-radius: var(--radius-lg);
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 350px;
            overflow-y: auto;
            white-space: pre-wrap;
            border: 1px solid rgba(0, 255, 136, 0.2);
            box-shadow: inset 0 0 20px rgba(0, 255, 136, 0.1);
        }

        .progress-log::-webkit-scrollbar {
            width: 8px;
        }

        .progress-log::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.3);
            border-radius: var(--radius-sm);
        }

        .progress-log::-webkit-scrollbar-thumb {
            background: var(--neon-green);
            border-radius: var(--radius-sm);
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        /* Advanced Progress Bars */
        .progress {
            background: var(--surface-secondary);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .progress-bar {
            background: var(--success-gradient);
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
            transition: var(--transition-normal);
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.5);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.7);
        }
    </style>
</head>
<body>
    <div class="bg-animation"></div>
    
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <div class="d-flex align-items-center justify-content-between">
                <h1><i class="fas fa-robot me-3"></i>LoRA Training Studio</h1>
                <div class="d-flex align-items-center gap-3">
                    <div class="status-indicator" id="training-status">
                        <i class="fas fa-circle text-success"></i>
                        <span class="text-white">Ready</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Layout -->
    <div class="main-container">
        <!-- Model Selector -->
        <div class="model-selector slide-in-left">
            <h3><i class="fas fa-cog me-2"></i>Choose Model</h3>
            <div class="model-slider" id="model-slider">
                <!-- Models will be loaded here -->
            </div>
        </div>

        <!-- Manager Banner -->
        <div id="unlimited-training-banner" class="unlimited-training-banner">
            <i class="fas fa-crown me-2"></i>
            <strong>MANAGER ACCESS:</strong> Unlimited training with no token deduction
            <i class="fas fa-infinity ms-2"></i>
        </div>

        <!-- Token Balance Display -->
        <div class="d-flex justify-content-end mb-3">
            <div id="token-display" class="token-balance" style="display: none;">
                <i class="fas fa-coins me-2"></i>
                <span id="token-count">0</span> tokens
                <span id="manager-badge" class="manager-badge" style="display: none;">MANAGER</span>
            </div>
        </div>

        <!-- Upload Section -->
        <div class="upload-section fade-in">
            <h3><i class="fas fa-upload me-2"></i>Upload Training Images</h3>
            
            <div class="upload-area" id="upload-area">
                <div class="upload-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <h4>Drag & Drop Images Here</h4>
                <p class="text-muted mb-3">or click to browse files</p>
                <button class="btn btn-primary btn-lg">
                    <i class="fas fa-folder-open me-2"></i>Browse Files
                </button>
                <input type="file" id="file-input" multiple accept="image/*" style="display: none;">
            </div>

            <!-- Image Grid -->
            <div id="image-grid-container" style="display: none;">
                <div class="d-flex justify-content-between align-items-center mt-4 mb-3">
                    <h5>Uploaded Images (<span id="image-count">0</span>)</h5>
                    <button class="btn btn-outline-danger btn-sm" onclick="clearAllImages()">
                        <i class="fas fa-trash me-2"></i>Clear All
                    </button>
                </div>
                <div class="image-grid" id="image-grid"></div>
            </div>

            <!-- Caption Section -->
            <div id="caption-section" style="display: none;" class="mt-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-comment-alt me-2"></i>Auto Caption</h5>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="generateCaptions()">
                            <i class="fas fa-magic me-2"></i>Generate with BLIP-2
                        </button>
                        <button class="btn btn-outline-secondary" onclick="showManualCaptions()">
                            <i class="fas fa-edit me-2"></i>Manual Edit
                        </button>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Concept/Trigger Word (optional)</label>
                    <input type="text" class="form-control" id="concept-sentence" 
                           placeholder="e.g., 'a photo of [trigger]'">
                </div>

                <div id="caption-progress" style="display: none;" class="mb-3">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             style="width: 0%"></div>
                    </div>
                    <small class="text-muted">Generating captions...</small>
                </div>

                <div id="caption-editor" style="display: none;"></div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="control-panel slide-in-right">
            <h3><i class="fas fa-sliders-h me-2"></i>Training Configuration</h3>
            
            <!-- Quick Presets -->
            <div class="mb-4">
                <label class="form-label">Quick Presets</label>
                <div class="btn-group w-100" role="group">
                    <input type="radio" class="btn-check" name="preset" id="preset-default" checked>
                    <label class="btn btn-outline-secondary" for="preset-default">Default</label>

                    <input type="radio" class="btn-check" name="preset" id="preset-fast">
                    <label class="btn btn-outline-primary" for="preset-fast">Fast</label>

                    <input type="radio" class="btn-check" name="preset" id="preset-balanced">
                    <label class="btn btn-outline-primary" for="preset-balanced">Balanced</label>

                    <input type="radio" class="btn-check" name="preset" id="preset-quality">
                    <label class="btn btn-outline-primary" for="preset-quality">Quality</label>
                </div>
            </div>

            <!-- LoRA Name -->
            <div class="mb-3">
                <label class="form-label">LoRA Name</label>
                <input type="text" class="form-control" id="lora-name"
                       placeholder="my-custom-lora">
            </div>

            <!-- Sample Prompt for Testing -->
            <div class="mb-3">
                <label class="form-label">Sample Prompt for Testing</label>
                <textarea class="form-control" id="sample-prompt" rows="3"
                          placeholder="Enter a prompt to test your LoRA during training (e.g., 'a photo of [trigger] in a garden')"></textarea>
                <small class="text-muted">This prompt will be used to generate 2 sample images every 250 steps during training. Use [trigger] to insert your concept word.</small>
            </div>

            <!-- Training Steps -->
            <div class="mb-3">
                <label class="form-label">Training Steps</label>
                <div class="d-flex align-items-center gap-2">
                    <input type="range" class="form-range" id="steps-range" 
                           min="100" max="5000" value="1000" step="100">
                    <input type="number" class="form-control" id="steps" 
                           style="width: 100px;" value="1000" min="100" max="5000">
                </div>
            </div>

            <!-- Learning Rate -->
            <div class="mb-3">
                <label class="form-label">Learning Rate</label>
                <div class="d-flex align-items-center gap-2">
                    <input type="range" class="form-range" id="lr-range" 
                           min="0.0001" max="0.01" value="0.001" step="0.0001">
                    <input type="number" class="form-control" id="learning-rate" 
                           style="width: 120px;" value="0.001" step="0.0001">
                </div>
            </div>

            <!-- Advanced Settings Toggle -->
            <div class="mb-3">
                <button class="btn btn-outline-secondary w-100" type="button" 
                        data-bs-toggle="collapse" data-bs-target="#advanced-settings">
                    <i class="fas fa-cogs me-2"></i>Advanced Settings
                </button>
            </div>

            <div class="collapse" id="advanced-settings">
                <!-- Rank -->
                <div class="mb-3">
                    <label class="form-label">Rank</label>
                    <input type="number" class="form-control" id="rank" value="16" min="4" max="128">
                </div>

                <!-- Batch Size -->
                <div class="mb-3">
                    <label class="form-label">Batch Size</label>
                    <input type="number" class="form-control" id="batch-size" value="1" min="1" max="8">
                </div>

                <!-- Alpha -->
                <div class="mb-3">
                    <label class="form-label">Alpha</label>
                    <input type="number" class="form-control" id="alpha" value="16" min="1" max="128">
                </div>
            </div>

            <!-- Training Summary -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title">Training Summary</h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="text-primary fw-bold" id="summary-images">0</div>
                            <small class="text-muted">Images</small>
                        </div>
                        <div class="col-6">
                            <div class="text-success fw-bold" id="summary-time">-</div>
                            <small class="text-muted">Est. Time</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Start Training Button -->
            <button class="btn btn-success btn-lg w-100 mt-4" id="start-training-btn" 
                    onclick="startTraining()" disabled>
                <i class="fas fa-rocket me-2"></i>Start Training
            </button>

            <!-- Training Progress -->
            <div id="training-progress" style="display: none;" class="mt-4">
                <div class="progress mb-2">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         id="training-progress-bar" style="width: 0%"></div>
                </div>
                <div class="d-flex justify-content-between">
                    <small class="text-muted">Training in progress...</small>
                    <button class="btn btn-outline-danger btn-sm" onclick="stopTraining()">
                        <i class="fas fa-stop me-1"></i>Stop
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Training Results Section -->
    <div id="training-results" style="display: none;" class="progress-monitor fade-in">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3><i class="fas fa-images me-2"></i>Training Results & Sample Images</h3>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshSamples()">
                        <i class="fas fa-sync me-1"></i>Refresh
                    </button>
                    <button class="btn btn-success btn-sm" onclick="downloadModel()" id="download-model-btn" disabled>
                        <i class="fas fa-download me-1"></i>Download Model
                    </button>
                </div>
            </div>

            <!-- Sample Images Grid -->
            <div class="mb-4">
                <h5><i class="fas fa-camera me-2"></i>Generated Samples During Training</h5>
                <div class="row" id="sample-images-grid">
                    <div class="col-12 text-center text-muted">
                        <i class="fas fa-image fa-3x mb-3"></i>
                        <p>Sample images will appear here during training</p>
                    </div>
                </div>
            </div>

            <!-- Model Information -->
            <div class="row">
                <div class="col-md-6">
                    <div class="stat-card">
                        <h6><i class="fas fa-info-circle me-2"></i>Model Information</h6>
                        <div id="model-info">
                            <p class="mb-1"><strong>Name:</strong> <span id="model-name-display">-</span></p>
                            <p class="mb-1"><strong>Status:</strong> <span id="model-status">Not Started</span></p>
                            <p class="mb-1"><strong>Total Steps:</strong> <span id="model-total-steps">-</span></p>
                            <p class="mb-0"><strong>Sample Prompt:</strong> <span id="model-sample-prompt">-</span></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="stat-card">
                        <h6><i class="fas fa-chart-bar me-2"></i>Training Statistics</h6>
                        <div id="training-stats">
                            <p class="mb-1"><strong>Images Trained:</strong> <span id="stats-images">-</span></p>
                            <p class="mb-1"><strong>Samples Generated:</strong> <span id="stats-samples">-</span></p>
                            <p class="mb-1"><strong>Training Time:</strong> <span id="stats-time">-</span></p>
                            <p class="mb-0"><strong>Model Size:</strong> <span id="stats-size">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Training Progress Monitor (Full Width) -->
    <div id="training-monitor" style="display: none;" class="progress-monitor fade-in">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3><i class="fas fa-chart-line me-2"></i>Training Progress Monitor</h3>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="toggleProgressLog()">
                        <i class="fas fa-terminal me-1"></i>Toggle Log
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="stopTraining()">
                        <i class="fas fa-stop me-1"></i>Stop Training
                    </button>
                </div>
            </div>

            <!-- Progress Stats -->
            <div class="progress-stats">
                <div class="stat-card">
                    <div class="stat-value" id="current-step">0</div>
                    <div class="stat-label">Current Step</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="total-steps">0</div>
                    <div class="stat-label">Total Steps</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="progress-percent">0%</div>
                    <div class="stat-label">Progress</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="elapsed-time">00:00</div>
                    <div class="stat-label">Elapsed Time</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="eta-time">--:--</div>
                    <div class="stat-label">ETA</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="current-loss">--</div>
                    <div class="stat-label">Current Loss</div>
                </div>
            </div>

            <!-- Main Progress Bar -->
            <div class="mb-4">
                <div class="d-flex justify-content-between mb-2">
                    <span class="fw-bold">Training Progress</span>
                    <span id="progress-text">0%</span>
                </div>
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                         id="main-progress-bar" style="width: 0%"></div>
                </div>
            </div>

            <!-- Training Log -->
            <div id="progress-log-container" style="display: none;">
                <h5><i class="fas fa-terminal me-2"></i>Training Log</h5>
                <div class="progress-log" id="training-log">
                    Initializing training...
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let selectedModel = null;
        let currentSessionId = null;
        let uploadedFiles = [];
        let captions = {};

        // Model configurations
        const MODELS = [
            {
                key: 'modal_train_lora_flux_24gb',
                name: 'FLUX.1 Dev',
                description: 'High-quality image generation with excellent detail',
                vram: '24GB',
                recommended: true,
                specs: ['flux', 'high-quality', '24gb']
            },
            {
                key: 'modal_train_lora_flux_schnell_24gb',
                name: 'FLUX.1 Schnell',
                description: 'Fast generation with good quality balance',
                vram: '24GB',
                recommended: false,
                specs: ['flux', 'fast', '24gb']
            },
            {
                key: 'train_lora_hidream_48',
                name: 'HiDream',
                description: 'High-resolution dream-like generation',
                vram: '48GB',
                recommended: false,
                specs: ['hidream', 'high-res', '48gb']
            },
            {
                key: 'train_lora_wan21_1b_24gb',
                name: 'WAN 2.1 1B',
                description: 'Efficient 1B parameter model for quick training',
                vram: '24GB',
                recommended: false,
                specs: ['wan21', 'efficient', '24gb']
            },
            {
                key: 'train_lora_wan21_14b_24gb',
                name: 'WAN 2.1 14B',
                description: 'Large 14B parameter model for maximum quality',
                vram: '24GB',
                recommended: false,
                specs: ['wan21', 'large', '24gb']
            }
        ];

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Modern UI initializing...');

            loadModels();
            setupFileUpload();
            setupPresets();
            setupRangeInputs();
            loadUserProfile();

            // Auto-select recommended model
            const recommendedModel = MODELS.find(m => m.recommended);
            if (recommendedModel) {
                selectModel(recommendedModel.key);
            }

            console.log('✅ Modern UI initialized');
        });

        // Load user profile and check manager status
        async function loadUserProfile() {
            const authToken = localStorage.getItem('authToken');
            if (!authToken) {
                // Redirect to auth if not logged in
                window.location.href = '/auth';
                return;
            }

            try {
                const response = await fetch('/api/user/profile', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateUserInterface(data);
                } else {
                    // Token might be expired
                    localStorage.removeItem('authToken');
                    window.location.href = '/auth';
                }
            } catch (error) {
                console.error('Failed to load user profile:', error);
                window.location.href = '/auth';
            }
        }

        function updateUserInterface(data) {
            const user = data.user;
            const balance = data.token_balance;
            const isManager = user.is_manager || user.manager_access || false;

            // Update token balance
            document.getElementById('token-count').textContent = balance.toLocaleString();

            // Show manager banner and update styling if user is manager
            const tokenDisplay = document.getElementById('token-display');
            const managerBadge = document.getElementById('manager-badge');
            const managerBanner = document.getElementById('unlimited-training-banner');

            if (isManager) {
                tokenDisplay.classList.add('manager');
                managerBadge.style.display = 'inline';
                managerBanner.style.display = 'block';
                document.getElementById('token-count').textContent = '∞';
            } else {
                tokenDisplay.classList.remove('manager');
                managerBadge.style.display = 'none';
                managerBanner.style.display = 'none';
            }

            // Show token display
            tokenDisplay.style.display = 'flex';

            // Store manager status globally
            window.isManager = isManager;
            window.userInfo = user;
        }

        function loadModels() {
            const container = document.getElementById('model-slider');

            let html = '';
            MODELS.forEach(model => {
                html += `
                    <div class="model-card ${model.recommended ? 'selected' : ''}"
                         onclick="selectModel('${model.key}')"
                         data-model="${model.key}">
                        <div class="model-card-content">
                            <div class="model-name">${model.name}</div>
                            <div class="model-desc">${model.description}</div>
                            <div class="model-specs">
                                ${model.specs.map(spec =>
                                    `<span class="spec-badge ${model.recommended && spec === 'high-quality' ? 'recommended' : ''}">${spec}</span>`
                                ).join('')}
                                ${model.recommended ? '<span class="spec-badge recommended">⭐ recommended</span>' : ''}
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function selectModel(modelKey) {
            console.log('🎯 Model selected:', modelKey);
            selectedModel = modelKey;

            // Update visual selection
            document.querySelectorAll('.model-card').forEach(card => {
                card.classList.remove('selected');
            });

            const selectedCard = document.querySelector(`[data-model="${modelKey}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');

                // Smooth scroll to selected model
                selectedCard.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest'
                });
            }

            updateTrainingSummary();
            showNotification(`Selected: ${MODELS.find(m => m.key === modelKey)?.name}`, 'success');
        }

        function setupFileUpload() {
            const uploadArea = document.getElementById('upload-area');
            const fileInput = document.getElementById('file-input');

            uploadArea.addEventListener('click', () => fileInput.click());

            fileInput.addEventListener('change', function(e) {
                handleFileUpload(e.target.files);
            });

            // Drag and drop
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#667eea';
                uploadArea.style.transform = 'translateY(-5px)';
            });

            uploadArea.addEventListener('dragleave', function() {
                uploadArea.style.borderColor = '#cbd5e0';
                uploadArea.style.transform = 'translateY(0)';
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#cbd5e0';
                uploadArea.style.transform = 'translateY(0)';
                handleFileUpload(e.dataTransfer.files);
            });
        }

        function handleFileUpload(files) {
            console.log('📤 Uploading files:', files.length);

            const formData = new FormData();
            let imageCount = 0;

            Array.from(files).forEach(file => {
                if (file.type.startsWith('image/')) {
                    formData.append('files', file);
                    imageCount++;
                }
            });

            if (imageCount === 0) {
                showNotification('Please select image files (JPG, PNG, GIF)', 'warning');
                return;
            }

            showNotification(`Uploading ${imageCount} images...`, 'info');

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showNotification(data.error, 'danger');
                    return;
                }

                currentSessionId = data.session_id;
                uploadedFiles = data.files;

                displayImages();
                showCaptionSection();
                updateCaptionSection();
                updateTrainingSummary();

                showNotification(`Successfully uploaded ${data.files.length} images`, 'success');
            })
            .catch(error => {
                console.error('Upload error:', error);
                showNotification('Upload failed', 'danger');
            });
        }

        function displayImages() {
            const container = document.getElementById('image-grid-container');
            const grid = document.getElementById('image-grid');
            const count = document.getElementById('image-count');

            if (uploadedFiles.length === 0) {
                container.style.display = 'none';
                return;
            }

            container.style.display = 'block';
            count.textContent = uploadedFiles.length;

            let html = '';
            uploadedFiles.forEach((file, index) => {
                html += `
                    <div class="image-card fade-in" style="animation-delay: ${index * 0.1}s">
                        <img src="/uploads/${currentSessionId}/${file.filename}" alt="${file.filename}">
                        <button class="remove-btn" onclick="removeImage(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="image-info">
                            <h6 class="mb-1">${file.filename}</h6>
                            <small class="text-muted">${formatFileSize(file.size)}</small>
                            ${file.width && file.height ? `<br><small class="text-muted">${file.width}×${file.height}</small>` : ''}
                        </div>
                    </div>
                `;
            });

            grid.innerHTML = html;
        }

        function removeImage(index) {
            const removedFile = uploadedFiles[index];
            uploadedFiles.splice(index, 1);

            // Remove caption for this file
            if (removedFile && captions[removedFile.filename]) {
                delete captions[removedFile.filename];
            }

            displayImages();
            updateCaptionSection();
            updateTrainingSummary();

            if (uploadedFiles.length === 0) {
                document.getElementById('caption-section').style.display = 'none';
                document.getElementById('caption-editor').style.display = 'none';
            }
        }

        function clearAllImages() {
            if (confirm('Remove all images?')) {
                uploadedFiles = [];
                captions = {};
                displayImages();
                updateCaptionSection();
                updateTrainingSummary();
                document.getElementById('caption-section').style.display = 'none';
                document.getElementById('caption-editor').style.display = 'none';
            }
        }

        function showCaptionSection() {
            const section = document.getElementById('caption-section');
            section.style.display = 'block';
            section.classList.add('fade-in');
        }

        function updateCaptionSection() {
            if (uploadedFiles.length === 0) {
                document.getElementById('caption-section').style.display = 'none';
                document.getElementById('caption-editor').style.display = 'none';
                return;
            }

            // Update image count in caption section
            const captionSection = document.getElementById('caption-section');
            if (captionSection.style.display === 'block') {
                // If caption editor is visible, update it
                const captionEditor = document.getElementById('caption-editor');
                if (captionEditor.style.display === 'block') {
                    showCaptionEditor();
                }
            }

            // Update the generate captions button text
            const generateBtn = captionSection.querySelector('button[onclick="generateCaptions()"]');
            if (generateBtn) {
                generateBtn.innerHTML = `<i class="fas fa-magic me-2"></i>Generate with BLIP-2 (${uploadedFiles.length} images)`;
            }

            // Update manual edit button text
            const manualBtn = captionSection.querySelector('button[onclick="showManualCaptions()"]');
            if (manualBtn) {
                manualBtn.innerHTML = `<i class="fas fa-edit me-2"></i>Manual Edit (${uploadedFiles.length} images)`;
            }
        }

        function generateCaptions() {
            if (!currentSessionId || uploadedFiles.length === 0) {
                showNotification('Please upload images first', 'warning');
                return;
            }

            const conceptSentence = document.getElementById('concept-sentence').value;
            const progressDiv = document.getElementById('caption-progress');
            const progressBar = progressDiv.querySelector('.progress-bar');

            progressDiv.style.display = 'block';
            progressBar.style.width = '0%';

            showNotification('Generating captions with BLIP-2...', 'info');

            fetch('/api/caption', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: currentSessionId,
                    concept_sentence: conceptSentence
                })
            })
            .then(response => response.json())
            .then(data => {
                progressBar.style.width = '100%';

                if (data.error) {
                    showNotification(data.error, 'danger');
                    return;
                }

                captions = data.captions;

                // Update caption editor if it's currently visible
                const captionEditor = document.getElementById('caption-editor');
                if (captionEditor.style.display === 'block') {
                    showCaptionEditor();
                }

                showNotification(data.message, 'success');

                setTimeout(() => {
                    progressDiv.style.display = 'none';
                }, 2000);
            })
            .catch(error => {
                console.error('Captioning error:', error);
                showNotification('Captioning failed', 'danger');
                progressDiv.style.display = 'none';
            });
        }

        function showManualCaptions() {
            if (!currentSessionId || uploadedFiles.length === 0) {
                showNotification('Please upload images first', 'warning');
                return;
            }

            // Initialize empty captions
            uploadedFiles.forEach(file => {
                if (!captions[file.filename]) {
                    captions[file.filename] = '';
                }
            });

            showCaptionEditor();
            showNotification('You can now manually edit captions', 'info');
        }

        function showCaptionEditor() {
            const editor = document.getElementById('caption-editor');

            if (uploadedFiles.length === 0) {
                editor.style.display = 'none';
                return;
            }

            let html = '<div class="row">';
            uploadedFiles.forEach((file, index) => {
                html += `
                    <div class="col-lg-6 col-xl-4 mb-3" data-file-index="${index}">
                        <div class="caption-editor-card">
                            <div class="d-flex p-3">
                                <div class="flex-shrink-0 me-3">
                                    <img src="/uploads/${currentSessionId}/${file.filename}"
                                         class="caption-image" alt="${file.filename}">
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-0 text-truncate flex-grow-1" title="${file.filename}">${file.filename}</h6>
                                        <button class="btn btn-outline-danger btn-sm ms-2"
                                                onclick="removeImageFromCaption(${index})"
                                                title="Remove image">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <textarea class="form-control caption-input"
                                              data-filename="${file.filename}"
                                              placeholder="Enter caption for this image..."
                                              rows="4">${captions[file.filename] || ''}</textarea>
                                    <small class="text-muted mt-1 d-block">
                                        ${formatFileSize(file.size)}
                                        ${file.width && file.height ? ` • ${file.width}×${file.height}` : ''}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';

            editor.innerHTML = html;
            editor.style.display = 'block';

            // Add event listeners with auto-save feedback
            document.querySelectorAll('.caption-input').forEach(input => {
                input.addEventListener('input', (e) => {
                    const filename = e.target.dataset.filename;
                    captions[filename] = e.target.value;

                    // Visual feedback for auto-save
                    e.target.style.borderColor = '#00d4ff';
                    setTimeout(() => {
                        e.target.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                    }, 500);
                });
            });
        }

        function removeImageFromCaption(index) {
            if (confirm('Remove this image?')) {
                removeImage(index);
            }
        }

        function setupPresets() {
            const presets = {
                default: { steps: 1000, lr: 0.001, rank: 16, batchSize: 1, alpha: 16 },
                fast: { steps: 500, lr: 0.002, rank: 8, batchSize: 2, alpha: 8 },
                balanced: { steps: 1000, lr: 0.001, rank: 16, batchSize: 1, alpha: 16 },
                quality: { steps: 2000, lr: 0.0005, rank: 32, batchSize: 1, alpha: 32 }
            };

            document.querySelectorAll('input[name="preset"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.checked) {
                        const preset = presets[this.id.replace('preset-', '')];
                        document.getElementById('steps').value = preset.steps;
                        document.getElementById('steps-range').value = preset.steps;
                        document.getElementById('learning-rate').value = preset.lr;
                        document.getElementById('lr-range').value = preset.lr;
                        document.getElementById('rank').value = preset.rank;
                        document.getElementById('batch-size').value = preset.batchSize;
                        document.getElementById('alpha').value = preset.alpha;
                        updateTrainingSummary();

                        showNotification(`Applied ${this.id.replace('preset-', '')} preset`, 'success');
                    }
                });
            });
        }

        function setupRangeInputs() {
            // Sync range and number inputs
            const pairs = [
                ['steps-range', 'steps'],
                ['lr-range', 'learning-rate']
            ];

            pairs.forEach(([rangeId, numberId]) => {
                const range = document.getElementById(rangeId);
                const number = document.getElementById(numberId);

                range.addEventListener('input', () => {
                    number.value = range.value;
                    updateTrainingSummary();
                });

                number.addEventListener('input', () => {
                    range.value = number.value;
                    updateTrainingSummary();
                });
            });

            // Update summary on other input changes
            ['lora-name', 'rank', 'batch-size', 'alpha'].forEach(id => {
                document.getElementById(id).addEventListener('input', updateTrainingSummary);
            });
        }

        function updateTrainingSummary() {
            const summaryImages = document.getElementById('summary-images');
            const summaryTime = document.getElementById('summary-time');
            const startBtn = document.getElementById('start-training-btn');
            const loraName = document.getElementById('lora-name').value;
            const steps = parseInt(document.getElementById('steps').value);

            // Update summary
            summaryImages.textContent = uploadedFiles.length;

            // Estimate training time
            if (uploadedFiles.length > 0 && steps) {
                const timePerStep = 2; // seconds per step
                const totalMinutes = Math.round((steps * timePerStep) / 60);
                if (totalMinutes < 60) {
                    summaryTime.textContent = `${totalMinutes}m`;
                } else {
                    const hours = Math.floor(totalMinutes / 60);
                    const minutes = totalMinutes % 60;
                    summaryTime.textContent = `${hours}h ${minutes}m`;
                }
            } else {
                summaryTime.textContent = '-';
            }

            // Enable/disable start button
            const canStart = selectedModel && uploadedFiles.length > 0 && loraName.trim();
            startBtn.disabled = !canStart;
        }

        function startTraining() {
            if (!selectedModel || !currentSessionId || uploadedFiles.length === 0) {
                showNotification('Please complete all steps first', 'warning');
                return;
            }

            const loraName = document.getElementById('lora-name').value.trim();
            if (!loraName) {
                showNotification('Please enter a LoRA name', 'warning');
                return;
            }

            const trainingData = {
                session_id: currentSessionId,
                config_name: selectedModel,
                lora_name: loraName,
                concept_sentence: document.getElementById('concept-sentence').value,
                sample_prompt: document.getElementById('sample-prompt').value,
                captions: captions,
                steps: parseInt(document.getElementById('steps').value),
                learning_rate: parseFloat(document.getElementById('learning-rate').value),
                rank: parseInt(document.getElementById('rank').value),
                batch_size: parseInt(document.getElementById('batch-size').value),
                alpha: parseInt(document.getElementById('alpha').value)
            };

            // Show training monitor and results
            document.getElementById('start-training-btn').style.display = 'none';
            document.getElementById('training-progress').style.display = 'block';
            document.getElementById('training-monitor').style.display = 'block';
            document.getElementById('training-results').style.display = 'block';

            // Initialize training stats
            document.getElementById('total-steps').textContent = trainingData.steps;
            document.getElementById('current-step').textContent = '0';
            document.getElementById('progress-percent').textContent = '0%';

            // Initialize model info
            document.getElementById('model-name-display').textContent = loraName;
            document.getElementById('model-status').textContent = 'Training Started';
            document.getElementById('model-total-steps').textContent = trainingData.steps;
            document.getElementById('model-sample-prompt').textContent = trainingData.sample_prompt || 'Default prompts';
            document.getElementById('stats-images').textContent = uploadedFiles.length;

            // Store current training info for later use
            window.currentTrainingInfo = {
                modelName: loraName,
                steps: trainingData.steps,
                startTime: Date.now()
            };

            // Scroll to monitor
            document.getElementById('training-monitor').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            fetch('/api/train', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(trainingData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showNotification(data.error, 'danger');
                    resetTrainingUI();
                    return;
                }

                showNotification(data.message, 'success');

                // Show manager privileges info if applicable
                if (data.manager_privileges) {
                    setTimeout(() => {
                        showNotification('Manager: No tokens deducted for this training', 'info');
                    }, 1000);
                }

                startProgressPolling();
                startTrainingTimer();
            })
            .catch(error => {
                console.error('Training error:', error);
                showNotification('Failed to start training', 'danger');
                resetTrainingUI();
            });
        }

        function stopTraining() {
            fetch('/api/training/stop', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                showNotification(data.message, 'info');
                resetTrainingUI();
            })
            .catch(error => {
                console.error('Stop training error:', error);
                showNotification('Failed to stop training', 'danger');
            });
        }

        function resetTrainingUI() {
            document.getElementById('start-training-btn').style.display = 'block';
            document.getElementById('training-progress').style.display = 'none';
            document.getElementById('training-monitor').style.display = 'none';

            // Clear timer
            if (window.trainingTimer) {
                clearInterval(window.trainingTimer);
            }
        }

        let trainingStartTime = null;

        function startTrainingTimer() {
            trainingStartTime = Date.now();

            window.trainingTimer = setInterval(() => {
                const elapsed = Date.now() - trainingStartTime;
                const elapsedFormatted = formatTime(elapsed);
                document.getElementById('elapsed-time').textContent = elapsedFormatted;

                // Calculate ETA based on progress
                const progressPercent = parseFloat(document.getElementById('progress-percent').textContent);
                if (progressPercent > 0) {
                    const totalEstimated = (elapsed / progressPercent) * 100;
                    const remaining = totalEstimated - elapsed;
                    const etaFormatted = formatTime(remaining);
                    document.getElementById('eta-time').textContent = etaFormatted;
                }
            }, 1000);
        }

        function formatTime(milliseconds) {
            const seconds = Math.floor(milliseconds / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);

            if (hours > 0) {
                return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
            }
        }

        function toggleProgressLog() {
            const logContainer = document.getElementById('progress-log-container');
            const isVisible = logContainer.style.display !== 'none';
            logContainer.style.display = isVisible ? 'none' : 'block';

            if (!isVisible) {
                // Auto-scroll to bottom when showing log
                const log = document.getElementById('training-log');
                log.scrollTop = log.scrollHeight;
            }
        }

        function appendToLog(message) {
            const log = document.getElementById('training-log');
            const timestamp = new Date().toLocaleTimeString();
            log.textContent += `[${timestamp}] ${message}\n`;

            // Auto-scroll to bottom
            log.scrollTop = log.scrollHeight;
        }

        function startProgressPolling() {
            const progressBar = document.getElementById('training-progress-bar');
            const mainProgressBar = document.getElementById('main-progress-bar');

            appendToLog('Starting training progress monitoring...');

            const pollInterval = setInterval(() => {
                fetch('/api/training/status')
                    .then(response => response.json())
                    .then(data => {
                        if (!data.is_training) {
                            clearInterval(pollInterval);

                            if (data.error) {
                                appendToLog(`Training failed: ${data.error}`);
                                showNotification('Training failed: ' + data.error, 'danger');
                                document.getElementById('model-status').textContent = 'Training Failed';
                            } else if (data.progress === 100) {
                                appendToLog('Training completed successfully!');
                                showNotification('Training completed successfully!', 'success');

                                // Update final stats
                                document.getElementById('progress-percent').textContent = '100%';
                                document.getElementById('current-step').textContent = document.getElementById('total-steps').textContent;
                                progressBar.style.width = '100%';
                                mainProgressBar.style.width = '100%';
                                document.getElementById('progress-text').textContent = '100%';
                                document.getElementById('model-status').textContent = 'Training Completed';

                                // Enable download button
                                document.getElementById('download-model-btn').disabled = false;

                                // Final refresh of samples
                                setTimeout(() => {
                                    refreshSamples();
                                }, 2000);
                            }

                            resetTrainingUI();
                        } else {
                            // Update progress bars
                            progressBar.style.width = data.progress + '%';
                            progressBar.textContent = data.progress + '%';
                            mainProgressBar.style.width = data.progress + '%';
                            document.getElementById('progress-text').textContent = data.progress + '%';

                            // Update detailed stats
                            document.getElementById('progress-percent').textContent = data.progress + '%';

                            // Calculate current step from progress
                            const totalSteps = parseInt(document.getElementById('total-steps').textContent);
                            const currentStep = Math.floor((data.progress / 100) * totalSteps);
                            document.getElementById('current-step').textContent = currentStep;

                            // Update loss if available
                            if (data.loss !== undefined) {
                                document.getElementById('current-loss').textContent = data.loss.toFixed(4);
                            }

                            // Log progress updates
                            if (data.message && data.message !== 'Training in progress') {
                                appendToLog(data.message);
                            }

                            // Log step updates every 10%
                            if (data.progress % 10 === 0 && data.progress > 0) {
                                appendToLog(`Progress: ${data.progress}% (Step ${currentStep}/${totalSteps})`);

                                // Refresh samples every 10% progress
                                refreshSamples();
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Progress polling error:', error);
                        appendToLog(`Error polling progress: ${error.message}`);
                    });
            }, 2000);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function refreshSamples() {
            if (!window.currentTrainingInfo) return;

            const modelName = window.currentTrainingInfo.modelName;

            fetch(`/api/models/${encodeURIComponent(modelName)}/samples`)
                .then(response => response.json())
                .then(data => {
                    displaySampleImages(data.samples);
                    document.getElementById('stats-samples').textContent = data.samples.length;
                })
                .catch(error => {
                    console.error('Error fetching samples:', error);
                });
        }

        function displaySampleImages(samples) {
            const grid = document.getElementById('sample-images-grid');

            if (samples.length === 0) {
                grid.innerHTML = `
                    <div class="col-12 text-center text-muted">
                        <i class="fas fa-image fa-3x mb-3"></i>
                        <p>No sample images generated yet</p>
                    </div>
                `;
                return;
            }

            let html = '';
            samples.forEach((sample, index) => {
                html += `
                    <div class="col-md-3 col-sm-6 mb-3">
                        <div class="caption-editor-card">
                            <img src="${sample.path}" class="w-100" style="height: 200px; object-fit: cover;" alt="Sample ${index + 1}">
                            <div class="p-2">
                                <small class="text-muted">${sample.filename}</small>
                                <br>
                                <small class="text-muted">${formatFileSize(sample.size)}</small>
                            </div>
                        </div>
                    </div>
                `;
            });

            grid.innerHTML = html;
        }

        function downloadModel() {
            if (!window.currentTrainingInfo) {
                showNotification('No model available for download', 'warning');
                return;
            }

            const modelName = window.currentTrainingInfo.modelName;
            const downloadUrl = `/api/models/${encodeURIComponent(modelName)}/download`;

            // Create a temporary link to trigger download
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = `${modelName}.zip`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification('Model download started', 'success');
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                border: none;
                border-radius: 15px;
            `;

            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
