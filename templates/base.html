<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AI Toolkit - LoRA Training{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --dark-color: #34495e;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            padding: 30px;
        }

        .navbar-custom {
            background: var(--primary-color);
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .card-custom {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card-custom:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .btn-custom {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary-custom {
            background: linear-gradient(45deg, var(--secondary-color), #5dade2);
            border: none;
            color: white;
        }

        .btn-primary-custom:hover {
            background: linear-gradient(45deg, #2980b9, var(--secondary-color));
            transform: translateY(-2px);
        }

        .btn-success-custom {
            background: linear-gradient(45deg, var(--success-color), #58d68d);
            border: none;
            color: white;
        }

        .btn-danger-custom {
            background: linear-gradient(45deg, var(--danger-color), #ec7063);
            border: none;
            color: white;
        }

        .upload-area {
            border: 3px dashed #bdc3c7;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .upload-area:hover {
            border-color: var(--secondary-color);
            background: #e3f2fd;
        }

        .upload-area.dragover {
            border-color: var(--success-color);
            background: #e8f5e8;
        }

        .progress-custom {
            height: 25px;
            border-radius: 15px;
            background: #ecf0f1;
        }

        .progress-bar-custom {
            background: linear-gradient(45deg, var(--success-color), #58d68d);
            border-radius: 15px;
        }

        .config-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .config-card.selected {
            border: 2px solid var(--secondary-color);
            background: #e3f2fd;
        }

        .image-preview {
            max-width: 100px;
            max-height: 100px;
            border-radius: 10px;
            object-fit: cover;
        }

        .caption-input {
            border-radius: 10px;
            border: 1px solid #ddd;
            padding: 10px;
            transition: border-color 0.3s ease;
        }

        .caption-input:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 144, 220, 0.25);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-training {
            background: var(--warning-color);
            animation: pulse 2s infinite;
        }

        .status-completed {
            background: var(--success-color);
        }

        .status-error {
            background: var(--danger-color);
        }

        .status-idle {
            background: #95a5a6;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .section-header {
            background: linear-gradient(45deg, var(--primary-color), var(--dark-color));
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .parameter-group {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .model-info {
            background: #e8f4fd;
            border-left: 4px solid var(--secondary-color);
            padding: 15px;
            border-radius: 0 10px 10px 0;
            margin-bottom: 15px;
        }

        .alert-custom {
            border-radius: 10px;
            border: none;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-robot me-2"></i>
                    AI Toolkit - LoRA Training
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text">
                        <span id="training-status" class="status-indicator status-idle"></span>
                        <span id="training-message">Ready</span>
                    </span>
                </div>
            </div>
        </nav>

        <div class="container">
            <div class="main-container">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-custom alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Global variables
        let currentSessionId = null;
        let selectedConfig = null;
        let uploadedFiles = [];
        let captions = {};

        // Training status polling
        function updateTrainingStatus() {
            fetch('/api/training/status')
                .then(response => response.json())
                .then(data => {
                    const statusIndicator = document.getElementById('training-status');
                    const statusMessage = document.getElementById('training-message');
                    
                    if (data.is_training) {
                        statusIndicator.className = 'status-indicator status-training';
                        statusMessage.textContent = `Training... ${data.progress}%`;
                    } else if (data.error) {
                        statusIndicator.className = 'status-indicator status-error';
                        statusMessage.textContent = 'Error: ' + data.error;
                    } else if (data.progress === 100) {
                        statusIndicator.className = 'status-indicator status-completed';
                        statusMessage.textContent = 'Training completed';
                    } else {
                        statusIndicator.className = 'status-indicator status-idle';
                        statusMessage.textContent = 'Ready';
                    }
                })
                .catch(error => {
                    console.error('Error fetching training status:', error);
                });
        }

        // Poll training status every 5 seconds
        setInterval(updateTrainingStatus, 5000);
        updateTrainingStatus(); // Initial call

        // Utility functions
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-custom alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.main-container');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
