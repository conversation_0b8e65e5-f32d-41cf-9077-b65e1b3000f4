<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}LoRA Training Studio{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            --accent-gradient: linear-gradient(135deg, #00d4ff 0%, #090979 50%, #020024 100%);
            --success-gradient: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            --bg-primary: #0a0a0f;
            --surface-primary: rgba(26, 26, 46, 0.95);
            --surface-glass: rgba(255, 255, 255, 0.05);
            --text-primary: #ffffff;
            --text-secondary: #b8c5d1;
            --neon-blue: #00d4ff;
            --neon-green: #00ff88;
            --shadow-neon: 0 0 20px rgba(0, 212, 255, 0.3);
            --radius-lg: 24px;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--primary-gradient);
            color: var(--text-primary);
            min-height: 100vh;
        }

        /* Navigation */
        .navbar-custom {
            background: var(--surface-primary);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-decoration: none;
        }

        .navbar-brand:hover {
            text-decoration: none;
            opacity: 0.8;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-link:hover {
            color: var(--neon-blue) !important;
            background: rgba(0, 212, 255, 0.1);
        }

        .nav-link.active {
            color: var(--neon-blue) !important;
            background: rgba(0, 212, 255, 0.2);
        }

        .token-balance {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid var(--neon-blue);
            border-radius: 20px;
            padding: 0.5rem 1rem;
            color: var(--neon-blue);
            font-weight: 600;
            margin-right: 1rem;
        }

        .token-balance.manager {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid var(--neon-green);
            color: var(--neon-green);
        }

        .manager-badge {
            background: var(--success-gradient);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 12px;
            transition: all 0.3s ease;
            text-decoration: none;
            color: var(--text-primary);
        }

        .user-profile:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            text-decoration: none;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--accent-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .user-info {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .user-tier {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .auth-buttons {
            display: flex;
            gap: 0.75rem;
        }

        .btn-auth {
            background: var(--accent-gradient);
            border: none;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-auth:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-neon);
            color: white;
            text-decoration: none;
        }

        .btn-auth.outline {
            background: transparent;
            border: 1px solid var(--neon-blue);
            color: var(--neon-blue);
        }

        .btn-auth.outline:hover {
            background: var(--neon-blue);
            color: var(--bg-primary);
        }

        /* Profile Dropdown */
        .profile-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--surface-primary);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 12px;
            padding: 1rem;
            min-width: 250px;
            box-shadow: var(--shadow-neon);
            backdrop-filter: blur(20px);
            z-index: 1000;
            display: none;
        }

        .profile-dropdown.show {
            display: block;
        }

        .dropdown-header {
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 1rem;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            border-radius: 8px;
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--neon-blue);
            text-decoration: none;
        }

        .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        .dropdown-divider {
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
            margin: 0.5rem 0;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .user-info {
                display: none;
            }
            
            .navbar-nav {
                margin-top: 1rem;
            }
            
            .token-balance {
                margin-right: 0;
                margin-bottom: 1rem;
            }
        }

        /* Content Area */
        .main-content {
            min-height: calc(100vh - 80px);
        }

        /* Animations */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .notification {
            animation: slideIn 0.3s ease;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-brain me-2"></i>LoRA Training Studio
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'home' %}active{% endif %}" href="/">
                            <i class="fas fa-home me-2"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'training' %}active{% endif %}" href="/training">
                            <i class="fas fa-rocket me-2"></i>Training
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'pricing' %}active{% endif %}" href="/pricing">
                            <i class="fas fa-credit-card me-2"></i>Pricing
                        </a>
                    </li>
                </ul>
                
                <div class="navbar-nav ms-auto d-flex align-items-center">
                    <!-- Token Balance (shown when logged in) -->
                    <div id="token-display" class="token-balance" style="display: none;">
                        <i class="fas fa-coins me-2"></i>
                        <span id="token-count">0</span> tokens
                        <span id="manager-badge" class="manager-badge" style="display: none;">MANAGER</span>
                    </div>
                    
                    <!-- User Profile (shown when logged in) -->
                    <div id="user-profile" class="user-profile position-relative" style="display: none;" onclick="toggleProfileDropdown()">
                        <div class="user-avatar" id="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-info">
                            <div class="user-name" id="user-name">User</div>
                            <div class="user-tier" id="user-tier">Starter</div>
                        </div>
                        <i class="fas fa-chevron-down ms-2"></i>
                        
                        <!-- Profile Dropdown -->
                        <div class="profile-dropdown" id="profile-dropdown">
                            <div class="dropdown-header">
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar me-3" id="dropdown-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <div class="user-name" id="dropdown-name">User</div>
                                        <div class="user-tier" id="dropdown-tier">Starter Plan</div>
                                    </div>
                                </div>
                            </div>
                            
                            <a href="/training" class="dropdown-item">
                                <i class="fas fa-rocket"></i>
                                Start Training
                            </a>
                            <a href="/pricing" class="dropdown-item">
                                <i class="fas fa-credit-card"></i>
                                Buy Tokens
                            </a>
                            <a href="#" class="dropdown-item" onclick="showSettings()">
                                <i class="fas fa-cog"></i>
                                Settings
                            </a>
                            <a href="#" class="dropdown-item" onclick="showProfile()">
                                <i class="fas fa-user-circle"></i>
                                Profile
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="dropdown-item" onclick="logout()">
                                <i class="fas fa-sign-out-alt"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                    
                    <!-- Auth Buttons (shown when not logged in) -->
                    <div id="auth-buttons" class="auth-buttons">
                        <a href="/auth" class="btn-auth outline">Sign In</a>
                        <a href="/auth" class="btn-auth">Get Started</a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Global Navigation JavaScript -->
    <script>
        let authToken = localStorage.getItem('authToken');
        let userInfo = null;

        // Initialize navigation
        document.addEventListener('DOMContentLoaded', function() {
            if (authToken) {
                loadUserProfile();
            } else {
                showAuthButtons();
            }
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                const dropdown = document.getElementById('profile-dropdown');
                const profile = document.getElementById('user-profile');
                
                if (profile && !profile.contains(event.target)) {
                    dropdown.classList.remove('show');
                }
            });
        });

        async function loadUserProfile() {
            try {
                const response = await fetch('/api/user/profile', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    userInfo = data.user;
                    
                    // Update UI with user info
                    updateUserInterface(data);
                    showUserProfile();
                } else {
                    // Token might be expired
                    logout();
                }
            } catch (error) {
                console.error('Failed to load user profile:', error);
                logout();
            }
        }

        function updateUserInterface(data) {
            const user = data.user;
            const balance = data.token_balance;
            const isManager = user.is_manager || user.manager_access || false;
            
            // Update token balance
            document.getElementById('token-count').textContent = balance.toLocaleString();
            
            // Show manager badge and update styling if user is manager
            const tokenDisplay = document.getElementById('token-display');
            const managerBadge = document.getElementById('manager-badge');
            
            if (isManager) {
                tokenDisplay.classList.add('manager');
                managerBadge.style.display = 'inline';
                document.getElementById('token-count').textContent = '∞';
            } else {
                tokenDisplay.classList.remove('manager');
                managerBadge.style.display = 'none';
            }
            
            // Update user info
            const userName = user.full_name || user.email.split('@')[0];
            const userInitials = userName.split(' ').map(n => n[0]).join('').toUpperCase();
            
            // Update all user name elements
            document.getElementById('user-name').textContent = userName;
            document.getElementById('dropdown-name').textContent = userName;
            
            // Update tier info
            const tierDisplay = isManager ? 'Manager' : (user.tier.charAt(0).toUpperCase() + user.tier.slice(1));
            document.getElementById('user-tier').textContent = tierDisplay;
            document.getElementById('dropdown-tier').textContent = isManager ? 'Manager Access' : `${tierDisplay} Plan`;
            
            // Update avatars
            if (user.profile_picture) {
                document.getElementById('user-avatar').innerHTML = `<img src="${user.profile_picture}" alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
                document.getElementById('dropdown-avatar').innerHTML = `<img src="${user.profile_picture}" alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
            } else {
                document.getElementById('user-avatar').textContent = userInitials;
                document.getElementById('dropdown-avatar').textContent = userInitials;
            }
            
            // Store manager status globally
            window.isManager = isManager;
        }

        function showUserProfile() {
            document.getElementById('auth-buttons').style.display = 'none';
            document.getElementById('token-display').style.display = 'flex';
            document.getElementById('user-profile').style.display = 'flex';
        }

        function showAuthButtons() {
            document.getElementById('auth-buttons').style.display = 'flex';
            document.getElementById('token-display').style.display = 'none';
            document.getElementById('user-profile').style.display = 'none';
        }

        function toggleProfileDropdown() {
            const dropdown = document.getElementById('profile-dropdown');
            dropdown.classList.toggle('show');
        }

        function logout() {
            authToken = null;
            userInfo = null;
            localStorage.removeItem('authToken');
            showAuthButtons();
            showNotification('Logged out successfully', 'info');
            
            // Redirect to home if on protected page
            if (window.location.pathname === '/training') {
                window.location.href = '/';
            }
        }

        function showProfile() {
            // Close dropdown
            document.getElementById('profile-dropdown').classList.remove('show');
            
            // Show profile modal (implement in individual pages)
            if (typeof showProfileModal === 'function') {
                showProfileModal();
            }
        }

        function showSettings() {
            // Close dropdown
            document.getElementById('profile-dropdown').classList.remove('show');
            
            // Show settings modal (implement in individual pages)
            if (typeof showSettingsModal === 'function') {
                showSettingsModal();
            }
        }

        function showNotification(message, type = 'info') {
            const colors = {
                success: '#00ff88',
                error: '#ff4757',
                warning: '#ffa502',
                info: '#00d4ff'
            };
            
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: ${colors[type]};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                z-index: 9999;
                max-width: 300px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            `;
            
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Auto-refresh token balance every 30 seconds if logged in
        setInterval(() => {
            if (authToken) {
                loadUserProfile();
            }
        }, 30000);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
