<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LoRA Training Studio - Manager Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            --accent-gradient: linear-gradient(135deg, #00d4ff 0%, #090979 50%, #020024 100%);
            --success-gradient: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            --danger-gradient: linear-gradient(135deg, #ff4757 0%, #c44569 100%);
            --bg-primary: #0a0a0f;
            --surface-primary: rgba(26, 26, 46, 0.95);
            --surface-glass: rgba(255, 255, 255, 0.05);
            --text-primary: #ffffff;
            --text-secondary: #b8c5d1;
            --neon-blue: #00d4ff;
            --neon-green: #00ff88;
            --neon-red: #ff4757;
            --shadow-neon: 0 0 20px rgba(0, 212, 255, 0.3);
            --radius-lg: 24px;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--primary-gradient);
            color: var(--text-primary);
            min-height: 100vh;
        }

        .manager-header {
            background: var(--surface-primary);
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            padding: 1rem 0;
            backdrop-filter: blur(20px);
        }

        .manager-title {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .manager-badge {
            background: var(--danger-gradient);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 1rem;
        }

        .auth-section {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .auth-card {
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            padding: 3rem;
            border: 1px solid rgba(0, 212, 255, 0.2);
            backdrop-filter: blur(20px);
            box-shadow: var(--shadow-neon);
            width: 100%;
            max-width: 400px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            padding: 2rem;
        }

        .stat-card {
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            padding: 2rem;
            border: 1px solid rgba(0, 212, 255, 0.2);
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-neon);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: 1.5rem;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 1rem;
        }

        .stat-icon.users { background: rgba(0, 212, 255, 0.2); color: var(--neon-blue); }
        .stat-icon.revenue { background: rgba(0, 255, 136, 0.2); color: var(--neon-green); }
        .stat-icon.usage { background: rgba(255, 71, 87, 0.2); color: var(--neon-red); }
        .stat-icon.system { background: rgba(255, 165, 2, 0.2); color: #ffa502; }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .stat-change {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            margin-top: 0.5rem;
            display: inline-block;
        }

        .stat-change.positive {
            background: rgba(0, 255, 136, 0.2);
            color: var(--neon-green);
        }

        .stat-change.negative {
            background: rgba(255, 71, 87, 0.2);
            color: var(--neon-red);
        }

        .users-table {
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            padding: 2rem;
            border: 1px solid rgba(0, 212, 255, 0.2);
            margin: 2rem;
        }

        .table-dark {
            background: transparent;
            color: var(--text-primary);
        }

        .table-dark th {
            border-color: rgba(255, 255, 255, 0.2);
            color: var(--neon-blue);
        }

        .table-dark td {
            border-color: rgba(255, 255, 255, 0.1);
        }

        .tier-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .tier-starter { background: rgba(108, 117, 125, 0.2); color: #6c757d; }
        .tier-professional { background: rgba(0, 212, 255, 0.2); color: var(--neon-blue); }
        .tier-enterprise { background: rgba(0, 255, 136, 0.2); color: var(--neon-green); }

        .btn-manager {
            background: var(--accent-gradient);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0.25rem;
        }

        .btn-manager:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-neon);
            color: white;
        }

        .btn-manager.danger {
            background: var(--danger-gradient);
        }

        .btn-manager.success {
            background: var(--success-gradient);
        }

        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: var(--text-primary);
            padding: 0.5rem;
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--neon-blue);
            box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.2);
            color: var(--text-primary);
        }

        .unlimited-training {
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            padding: 2rem;
            border: 2px solid var(--neon-green);
            margin: 2rem;
            box-shadow: 0 0 30px rgba(0, 255, 136, 0.3);
        }

        .unlimited-badge {
            background: var(--success-gradient);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1rem;
        }

        .training-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            color: white;
            z-index: 1000;
            max-width: 300px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .notification.success { background: var(--success-gradient); }
        .notification.error { background: var(--danger-gradient); }
        .notification.info { background: var(--accent-gradient); }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
                padding: 1rem;
            }
            
            .users-table, .unlimited-training {
                margin: 1rem;
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Authentication Section -->
    <div id="auth-section" class="auth-section">
        <div class="auth-card">
            <h3 class="text-center mb-4">Manager Authentication</h3>
            <form onsubmit="authenticateManager(event)">
                <div class="mb-3">
                    <label class="form-label">Username</label>
                    <input type="text" class="form-control" id="manager-username" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-control" id="manager-password" required>
                </div>
                <button type="submit" class="btn btn-manager w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>Authenticate
                </button>
            </form>
        </div>
    </div>

    <!-- Manager Dashboard -->
    <div id="dashboard-content" style="display: none;">
        <!-- Header -->
        <div class="manager-header">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h1 class="manager-title">LoRA Training Studio</h1>
                        <span class="manager-badge">MANAGER</span>
                    </div>
                    <div>
                        <button class="btn btn-manager" onclick="createBackup()">
                            <i class="fas fa-download me-2"></i>Backup
                        </button>
                        <button class="btn btn-manager danger" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Dashboard -->
        <div class="dashboard-grid">
            <!-- Users Stats -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stat-value" id="total-users">0</div>
                        <div class="stat-label">Total Users</div>
                        <div class="stat-change positive" id="new-users">+0 this week</div>
                    </div>
                </div>
                <div class="mt-3">
                    <small class="text-muted">Verified: <span id="verified-users">0</span></small>
                </div>
            </div>

            <!-- Revenue Stats -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon revenue">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stat-value" id="total-revenue">$0</div>
                        <div class="stat-label">Total Revenue</div>
                        <div class="stat-change positive" id="revenue-week">+$0 this week</div>
                    </div>
                </div>
                <div class="mt-3">
                    <small class="text-muted">Tokens Sold: <span id="tokens-sold">0</span></small>
                </div>
            </div>

            <!-- Usage Stats -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon usage">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stat-value" id="training-jobs">0</div>
                        <div class="stat-label">Training Jobs</div>
                        <div class="stat-change positive" id="tokens-consumed">0 tokens used</div>
                    </div>
                </div>
                <div class="mt-3">
                    <small class="text-muted">Models Created: <span id="models-created">0</span></small>
                </div>
            </div>

            <!-- System Stats -->
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon system">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stat-value" id="disk-usage">0 GB</div>
                        <div class="stat-label">Disk Usage</div>
                        <div class="stat-change" id="system-status">System Online</div>
                    </div>
                </div>
                <div class="mt-3">
                    <small class="text-muted">Training: <span id="training-status">Idle</span></small>
                </div>
            </div>
        </div>

        <!-- Unlimited Training Section -->
        <div class="unlimited-training">
            <div class="unlimited-badge">
                <i class="fas fa-infinity me-2"></i>UNLIMITED TRAINING ACCESS
            </div>
            <h4>Manager Training Controls</h4>
            <p class="text-secondary">Train models without token limitations. Full access to all configurations and unlimited resources.</p>
            
            <div class="training-controls">
                <div>
                    <label class="form-label">LoRA Name</label>
                    <input type="text" class="form-control" id="unlimited-lora-name" placeholder="manager-model-v1">
                </div>
                <div>
                    <label class="form-label">Training Steps</label>
                    <input type="number" class="form-control" id="unlimited-steps" value="2000" min="100">
                </div>
                <div>
                    <label class="form-label">Concept Sentence</label>
                    <input type="text" class="form-control" id="unlimited-concept" placeholder="my custom concept">
                </div>
                <div>
                    <label class="form-label">Sample Prompt</label>
                    <input type="text" class="form-control" id="unlimited-prompt" placeholder="a photo of [trigger]">
                </div>
            </div>
            
            <div class="mt-3">
                <button class="btn btn-manager success" onclick="startUnlimitedTraining()">
                    <i class="fas fa-rocket me-2"></i>Start Unlimited Training
                </button>
                <button class="btn btn-manager" onclick="window.open('/', '_blank')">
                    <i class="fas fa-external-link-alt me-2"></i>Open Training Interface
                </button>
            </div>
        </div>

        <!-- Users Management Table -->
        <div class="users-table">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fas fa-users me-2"></i>User Management</h4>
                <button class="btn btn-manager" onclick="refreshUsers()">
                    <i class="fas fa-sync me-2"></i>Refresh
                </button>
            </div>
            
            <div class="table-responsive">
                <table class="table table-dark table-hover">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Tier</th>
                            <th>Balance</th>
                            <th>Spent</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body">
                        <!-- Users loaded dynamically -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let managerAuthenticated = false;
        let currentSessionId = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
        });

        function checkAuthentication() {
            const authStatus = localStorage.getItem('managerAuth');
            if (authStatus === 'true') {
                showDashboard();
            } else {
                showAuthSection();
            }
        }

        async function authenticateManager(event) {
            event.preventDefault();

            const username = document.getElementById('manager-username').value;
            const password = document.getElementById('manager-password').value;

            try {
                const response = await fetch('/api/manager/auth', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (data.success) {
                    localStorage.setItem('managerAuth', 'true');
                    managerAuthenticated = true;
                    showDashboard();
                    showNotification('Manager authenticated successfully', 'success');
                } else {
                    showNotification(data.error || 'Authentication failed', 'error');
                }
            } catch (error) {
                showNotification('Network error. Please try again.', 'error');
            }
        }

        function showAuthSection() {
            document.getElementById('auth-section').style.display = 'flex';
            document.getElementById('dashboard-content').style.display = 'none';
        }

        function showDashboard() {
            document.getElementById('auth-section').style.display = 'none';
            document.getElementById('dashboard-content').style.display = 'block';
            loadDashboardData();
        }

        function logout() {
            localStorage.removeItem('managerAuth');
            managerAuthenticated = false;
            showAuthSection();
            showNotification('Logged out successfully', 'info');
        }

        async function loadDashboardData() {
            try {
                // Load system statistics
                const statsResponse = await fetch('/api/manager/stats');
                const stats = await statsResponse.json();

                if (stats.users) {
                    document.getElementById('total-users').textContent = stats.users.total;
                    document.getElementById('verified-users').textContent = stats.users.verified;
                    document.getElementById('new-users').textContent = `+${stats.users.new_this_week} this week`;
                }

                if (stats.revenue) {
                    document.getElementById('total-revenue').textContent = `$${stats.revenue.total.toFixed(2)}`;
                    document.getElementById('revenue-week').textContent = `+$${stats.revenue.this_week.toFixed(2)} this week`;
                    document.getElementById('tokens-sold').textContent = stats.revenue.tokens_sold.toLocaleString();
                }

                if (stats.usage) {
                    document.getElementById('training-jobs').textContent = stats.usage.training_jobs;
                    document.getElementById('tokens-consumed').textContent = `${stats.revenue.tokens_consumed.toLocaleString()} tokens used`;
                    document.getElementById('models-created').textContent = stats.usage.models_created;
                }

                if (stats.usage && stats.usage.disk_usage) {
                    const diskGB = (stats.usage.disk_usage / (1024 * 1024 * 1024)).toFixed(2);
                    document.getElementById('disk-usage').textContent = `${diskGB} GB`;
                }

                document.getElementById('system-status').textContent = stats.system.billing_enabled ? 'System Online' : 'Billing Offline';
                document.getElementById('training-status').textContent = stats.system.training_active ? 'Active' : 'Idle';

                // Load users
                await loadUsers();

            } catch (error) {
                console.error('Failed to load dashboard data:', error);
                showNotification('Failed to load dashboard data', 'error');
            }
        }

        async function loadUsers() {
            try {
                const response = await fetch('/api/manager/users');
                const data = await response.json();

                if (data.users) {
                    displayUsers(data.users);
                }
            } catch (error) {
                console.error('Failed to load users:', error);
            }
        }

        function displayUsers(users) {
            const tbody = document.getElementById('users-table-body');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div>
                            <strong>${user.name}</strong><br>
                            <small class="text-muted">${user.email}</small>
                        </div>
                    </td>
                    <td>
                        <span class="tier-badge tier-${user.tier}">${user.tier}</span>
                    </td>
                    <td>
                        <strong>${user.token_balance.toLocaleString()}</strong><br>
                        <small class="text-muted">${user.recent_jobs} jobs</small>
                    </td>
                    <td>
                        <strong>${user.total_spent.toLocaleString()}</strong><br>
                        <small class="text-muted">${user.recent_tokens.toLocaleString()} recent</small>
                    </td>
                    <td>
                        <span class="badge ${user.email_verified ? 'bg-success' : 'bg-warning'}">
                            ${user.email_verified ? 'Verified' : 'Pending'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-manager btn-sm" onclick="addTokensToUser('${user.user_id}')">
                            <i class="fas fa-plus"></i> Tokens
                        </button>
                        <button class="btn btn-manager btn-sm" onclick="changeTier('${user.user_id}', '${user.tier}')">
                            <i class="fas fa-crown"></i> Tier
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        async function addTokensToUser(userId) {
            const amount = prompt('Enter token amount to add:');
            const reason = prompt('Reason for token credit:') || 'Manager credit';

            if (amount && !isNaN(amount) && parseInt(amount) > 0) {
                try {
                    const response = await fetch(`/api/manager/user/${userId}/tokens`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            amount: parseInt(amount),
                            reason: reason
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        showNotification(`Added ${amount} tokens successfully`, 'success');
                        loadUsers(); // Refresh users table
                    } else {
                        showNotification(data.error || 'Failed to add tokens', 'error');
                    }
                } catch (error) {
                    showNotification('Network error', 'error');
                }
            }
        }

        async function changeTier(userId, currentTier) {
            const tiers = ['starter', 'professional', 'enterprise'];
            const newTier = prompt(`Change tier from ${currentTier} to (starter/professional/enterprise):`);

            if (newTier && tiers.includes(newTier.toLowerCase())) {
                try {
                    const response = await fetch(`/api/manager/user/${userId}/tier`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ tier: newTier.toLowerCase() })
                    });

                    const data = await response.json();

                    if (data.success) {
                        showNotification(`Tier updated to ${newTier}`, 'success');
                        loadUsers(); // Refresh users table
                    } else {
                        showNotification(data.error || 'Failed to update tier', 'error');
                    }
                } catch (error) {
                    showNotification('Network error', 'error');
                }
            }
        }

        async function startUnlimitedTraining() {
            const loraName = document.getElementById('unlimited-lora-name').value;
            const steps = document.getElementById('unlimited-steps').value;
            const concept = document.getElementById('unlimited-concept').value;
            const prompt = document.getElementById('unlimited-prompt').value;

            if (!loraName) {
                showNotification('Please enter a LoRA name', 'error');
                return;
            }

            // First, we need to upload some files or use existing session
            if (!currentSessionId) {
                showNotification('Please upload training images first', 'error');
                return;
            }

            try {
                const response = await fetch('/api/manager/training/unlimited', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        session_id: currentSessionId,
                        config_name: 'modal_train_lora_flux_24gb', // Default config
                        lora_name: loraName,
                        concept_sentence: concept,
                        sample_prompt: prompt,
                        steps: parseInt(steps),
                        learning_rate: 1e-4,
                        rank: 16,
                        batch_size: 1,
                        captions: {} // Empty captions for now
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showNotification('Unlimited training started! No tokens deducted.', 'success');
                } else {
                    showNotification(data.error || 'Failed to start training', 'error');
                }
            } catch (error) {
                showNotification('Network error', 'error');
            }
        }

        async function createBackup() {
            try {
                const response = await fetch('/api/manager/system/backup', {
                    method: 'POST'
                });

                const data = await response.json();

                if (data.success) {
                    showNotification(`Backup created: ${data.backup_name}`, 'success');
                } else {
                    showNotification(data.error || 'Backup failed', 'error');
                }
            } catch (error) {
                showNotification('Network error', 'error');
            }
        }

        function refreshUsers() {
            loadUsers();
            showNotification('Users refreshed', 'info');
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Auto-refresh dashboard every 30 seconds
        setInterval(() => {
            if (managerAuthenticated) {
                loadDashboardData();
            }
        }, 30000);
    </script>
</body>
</html>
