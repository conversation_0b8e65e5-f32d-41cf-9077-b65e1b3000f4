# AI Toolkit Flask Application

An advanced Flask web application for training LoRA models with image/video upload, auto-captioning, and configurable training parameters.

## Features

- **File Upload**: Drag & drop interface for images, videos, and text files
- **Auto-Captioning**: AI-powered image captioning using Florence-2 model
- **Model Configuration**: Choose from available YAML configuration files
- **Training Parameters**: Customize steps, learning rate, rank, batch size, etc.
- **Real-time Progress**: Monitor training progress with live updates
- **Responsive UI**: Modern, mobile-friendly interface

## Installation

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Ensure AI Toolkit is Set Up**:
   Make sure you have the AI Toolkit properly configured with your models and configurations.

3. **Run the Application**:
   ```bash
   python app.py
   ```

4. **Access the Interface**:
   Open your browser and go to `http://localhost:5000`

## Usage

### Step 1: Upload Files
- Drag and drop images/videos into the upload area
- Supported formats: PNG, JPG, JPEG, GIF, MP4, AVI, MOV, TXT
- You can also click "Browse Files" to select files manually

### Step 2: Select Model Configuration
- Choose from available YAML configuration files
- Each config shows model information (FLUX, architecture, etc.)
- The interface displays model details when selected

### Step 3: Configure Captions
- **Auto-Captioning**: Use AI to generate captions automatically
- **Manual Captions**: Edit captions manually for each image
- **Trigger Word**: Optional concept/trigger word to include in captions

### Step 4: Set Training Parameters
- **LoRA Name**: Unique name for your trained model
- **Training Steps**: Number of training iterations (100-10000)
- **Learning Rate**: Training learning rate (1e-6 to 1e-2)
- **LoRA Rank**: Network rank parameter (4-128)
- **Batch Size**: Training batch size (1-8)

### Step 5: Start Training
- Click "Start Training" to begin the process
- Monitor progress in real-time
- Training runs in the background
- Stop training if needed

## Configuration Files

The application reads YAML configuration files from the `config/` folder. Each config file should follow the AI Toolkit format:

```yaml
---
job: extension
config:
  name: "my_lora_model"
  process:
    - type: 'sd_trainer'
      training_folder: "output"
      device: cuda:0
      network:
        type: "lora"
        linear: 16
        linear_alpha: 16
      model:
        name_or_path: "black-forest-labs/FLUX.1-dev"
        is_flux: true
        quantize: true
      train:
        steps: 1000
        lr: 1e-4
        batch_size: 1
        # ... other training parameters
      datasets:
        - folder_path: "/path/to/dataset"
          caption_ext: "txt"
          # ... other dataset parameters
```

## API Endpoints

### File Management
- `POST /upload` - Upload files
- `GET /uploads/<session_id>/<filename>` - Serve uploaded files

### Configuration
- `GET /api/configs` - Get all available configurations
- `GET /api/config/<config_name>` - Get specific configuration details

### Captioning
- `POST /api/caption` - Generate AI captions for images

### Training
- `POST /api/train` - Start training with parameters
- `GET /api/training/status` - Get current training status
- `POST /api/training/stop` - Stop current training

## File Structure

```
├── app.py                 # Main Flask application
├── templates/
│   ├── base.html         # Base template with styling
│   └── index.html        # Main interface template
├── utils/
│   ├── config_parser.py  # Configuration file parser
│   └── training_manager.py # Training job manager
├── uploads/              # Temporary file uploads
├── datasets/             # Generated training datasets
├── output/               # Training outputs
├── tmp/                  # Temporary config files
└── config/               # YAML configuration files
```

## Utilities

### ConfigParser
- Parses YAML configuration files
- Extracts model, training, and network parameters
- Validates configuration structure

### TrainingManager
- Manages training jobs and status
- Creates datasets from uploaded files
- Handles training progress tracking
- Provides callbacks for training events

## Customization

### Adding New Models
1. Create a new YAML configuration file in the `config/` folder
2. Follow the AI Toolkit configuration format
3. The application will automatically detect and display it

### Styling
- Modify `templates/base.html` for global styling changes
- CSS uses Bootstrap 5 with custom styling
- Responsive design works on mobile devices

### Training Parameters
- Modify validation ranges in `utils/training_manager.py`
- Add new parameters to the training interface
- Update the config preparation logic as needed

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure AI Toolkit is properly installed and in the Python path
2. **CUDA Errors**: Check GPU availability and CUDA installation
3. **Memory Issues**: Reduce batch size or enable low VRAM mode
4. **Config Errors**: Validate YAML syntax and required fields

### Logs
- Check console output for detailed error messages
- Training progress and errors are displayed in the web interface
- Flask debug mode provides detailed error information

## Security Notes

- Change the Flask secret key in production
- Implement proper file upload validation
- Consider adding authentication for production use
- Limit file upload sizes and types as needed

## Performance Tips

- Use GPU acceleration when available
- Monitor system resources during training
- Clean up old files periodically
- Use appropriate batch sizes for your hardware

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This application is built on top of the AI Toolkit. Please refer to the original project's license terms.
